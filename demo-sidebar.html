<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Icon-Only Sidebar</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
        }

        /* Icon-Only Sidebar */
        .track-sidebar {
            width: 70px;
            background: var(--sidebar-bg-color, #ffffff);
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        /* Logo/Brand Section */
        .sidebar-brand {
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color, #2c3e50);
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .sidebar-brand img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Navigation Icons */
        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 15px;
            flex: 1;
        }

        .nav-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--sidebar-text-color, #666);
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
        }

        .nav-icon:hover {
            background: var(--sidebar-hover-color, #f0f0f0);
            color: var(--primary-color, #2c3e50);
            transform: translateY(-2px);
        }

        .nav-icon.active {
            background: var(--primary-color, #2c3e50);
            color: white;
        }

        .nav-icon i {
            font-size: 18px;
        }

        /* Notification Badge */
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--secondary-color, #e74c3c);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* Tooltip */
        .nav-icon::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .nav-icon:hover::after {
            opacity: 1;
            visibility: visible;
            left: 65px;
        }

        /* Main Content Area */
        .main-content {
            margin-left: 70px;
            flex: 1;
            padding: 20px;
            background: white;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--primary-color, #2c3e50);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .modal-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
        }

        .tab-btn {
            flex: 1;
            padding: 15px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: var(--primary-color, #2c3e50);
            border-bottom-color: var(--primary-color, #2c3e50);
        }

        .modal-body {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Demo Content */
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        .demo-section h3 {
            margin-bottom: 15px;
            color: var(--primary-color, #2c3e50);
        }

        /* CSS Variables for theming */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #e74c3c;
            --accent-color: #3498db;
            --sidebar-bg-color: #ffffff;
            --sidebar-text-color: #666666;
            --sidebar-hover-color: #f0f0f0;
        }

        /* Theme variations for demo */
        .theme-dark {
            --sidebar-bg-color: #1a1a1a;
            --sidebar-text-color: #ffffff;
            --sidebar-hover-color: #333333;
        }

        .theme-blue {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --sidebar-bg-color: #ecf0f1;
        }
    </style>
</head>
<body>
    <!-- Icon-Only Sidebar -->
    <div class="track-sidebar">
        <!-- Logo/Brand -->
        <div class="sidebar-brand" id="sidebar-brand">
            E
        </div>

        <!-- Navigation Icons -->
        <div class="sidebar-nav">
            <button class="nav-icon" data-tooltip="Documents" onclick="openDocumentsModal()">
                <i class="fas fa-file-alt"></i>
                <span class="notification-badge">3</span>
            </button>

            <button class="nav-icon" data-tooltip="Notifications" onclick="openNotificationsModal()">
                <i class="fas fa-bell"></i>
                <span class="notification-badge">5</span>
            </button>

            <button class="nav-icon active" data-tooltip="Shipment Details" onclick="scrollToSection('shipment-details')">
                <i class="fas fa-box"></i>
            </button>

            <button class="nav-icon" data-tooltip="Tracking History" onclick="scrollToSection('tracking-history')">
                <i class="fas fa-route"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="demo-section">
            <h3>Demo: Icon-Only Sidebar for Track Page</h3>
            <p>This demo shows the new icon-only sidebar design with:</p>
            <ul>
                <li>70px width with centered icons</li>
                <li>Hover tooltips for better UX</li>
                <li>Notification badges</li>
                <li>Smooth hover animations</li>
                <li>Theming support via CSS variables</li>
            </ul>
            
            <h4>Theme Controls:</h4>
            <button onclick="setTheme('default')">Default Theme</button>
            <button onclick="setTheme('dark')">Dark Theme</button>
            <button onclick="setTheme('blue')">Blue Theme</button>
        </div>

        <div class="demo-section" id="shipment-details">
            <h3>Shipment Details Section</h3>
            <p>This section would contain shipment information. Clicking the shipment details icon will smooth scroll here.</p>
        </div>

        <div class="demo-section" id="tracking-history">
            <h3>Tracking History Section</h3>
            <p>This section would contain tracking timeline. Clicking the tracking history icon will smooth scroll here.</p>
        </div>
    </div>    <!-- Documents Modal -->
    <div class="modal" id="documentsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Document Requests</h3>
                <button class="close-btn" onclick="closeModal('documentsModal')">&times;</button>
            </div>
            <div class="modal-tabs">
                <button class="tab-btn active" onclick="switchTab('pending')">Pending Upload</button>
                <button class="tab-btn" onclick="switchTab('cancelled')">Cancelled</button>
                <button class="tab-btn" onclick="switchTab('approved')">Approved</button>
            </div>
            <div class="modal-body">
                <div class="tab-content active" id="pending-tab">
                    <p>Pending document requests would be listed here...</p>
                </div>
                <div class="tab-content" id="cancelled-tab">
                    <p>Cancelled document requests would be listed here...</p>
                </div>
                <div class="tab-content" id="approved-tab">
                    <p>Approved documents would be listed here with download links...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Modal -->
    <div class="modal" id="notificationsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Notifications</h3>
                <button class="close-btn" onclick="closeModal('notificationsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <button onclick="markAllAsRead()" style="margin-bottom: 15px; padding: 8px 16px; background: var(--primary-color); color: white; border: none; border-radius: 4px; cursor: pointer;">Mark All as Read</button>
                <p>Notifications would be listed here...</p>
            </div>
        </div>
    </div>

    <script>
        // Theme switching
        function setTheme(theme) {
            document.body.className = theme === 'default' ? '' : `theme-${theme}`;
        }

        // Modal functions
        function openDocumentsModal() {
            document.getElementById('documentsModal').classList.add('show');
        }

        function openNotificationsModal() {
            document.getElementById('notificationsModal').classList.add('show');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // Tab switching
        function switchTab(tabName) {
            // Remove active from all tabs and content
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active to clicked tab and corresponding content
            event.target.classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        // Smooth scrolling
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Mark all notifications as read
        function markAllAsRead() {
            alert('All notifications marked as read!');
            // Remove notification badges
            document.querySelectorAll('.notification-badge').forEach(badge => {
                badge.style.display = 'none';
            });
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.classList.remove('show');
            }
        }

        // Demo: Update brand based on settings (simulate favicon or first letter)
        function updateBrand() {
            const brand = document.getElementById('sidebar-brand');
            // This would normally pull from settings
            // For demo, we'll show first letter of app name
            brand.textContent = 'E'; // First letter of "ELTA"
        }

        updateBrand();
    </script>
</body>
</html>