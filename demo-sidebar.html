<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Sidebar - Track Page</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow: hidden;
        }

        .demo-container {
            display: flex;
            height: 100vh;
        }

        /* New Icon Sidebar Design */
        .new-sidebar {
            width: 70px;
            background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .sidebar-brand {
            margin-bottom: 40px;
            padding: 10px;
        }

        .sidebar-brand i {
            font-size: 28px;
            color: #ffffff;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 12px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .sidebar-icons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            flex: 1;
        }

        .sidebar-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            color: #ffffff;
            font-size: 18px;
        }

        .sidebar-icon:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .sidebar-icon.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .sidebar-icon .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .sidebar-tooltip {
            position: absolute;
            left: 75px;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sidebar-tooltip::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #333;
        }

        .sidebar-icon:hover .sidebar-tooltip {
            opacity: 1;
            visibility: visible;
        }

        .sidebar-bottom {
            margin-top: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* Panel Styles */
        .sidebar-panel {
            position: fixed;
            top: 0;
            left: 70px;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 999;
            overflow-y: auto;
        }

        .sidebar-panel.active {
            transform: translateX(0);
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-header h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .panel-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .panel-close:hover {
            background: #e9ecef;
            color: #333;
        }

        .panel-content {
            padding: 20px;
        }

        .demo-content {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .demo-content h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 32px;
            font-weight: 700;
        }

        .demo-content p {
            color: #666;
            font-size: 18px;
            max-width: 600px;
            line-height: 1.6;
        }

        .demo-features {
            margin-top: 40px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            max-width: 800px;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: left;
        }

        .feature-card i {
            font-size: 24px;
            color: #667eea;
            margin-bottom: 10px;
        }

        .feature-card h4 {
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
            font-weight: 600;
        }

        .feature-card p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- New Icon Sidebar -->
        <div class="new-sidebar">
            <!-- Brand/Logo -->
            <div class="sidebar-brand">
                <i class="fas fa-shipping-fast"></i>
            </div>

            <!-- Main Icons -->
            <div class="sidebar-icons">
                <div class="sidebar-icon active" data-panel="tracking">
                    <i class="fas fa-search"></i>
                    <div class="sidebar-tooltip">Track Shipment</div>
                </div>

                <div class="sidebar-icon" data-panel="documents">
                    <i class="fas fa-file-alt"></i>
                    <span class="badge">3</span>
                    <div class="sidebar-tooltip">Documents</div>
                </div>

                <div class="sidebar-icon" data-panel="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="badge">5</span>
                    <div class="sidebar-tooltip">Notifications</div>
                </div>

                <div class="sidebar-icon" data-panel="details">
                    <i class="fas fa-info-circle"></i>
                    <div class="sidebar-tooltip">Shipment Details</div>
                </div>

                <div class="sidebar-icon" data-panel="map">
                    <i class="fas fa-map-marker-alt"></i>
                    <div class="sidebar-tooltip">Track on Map</div>
                </div>

                <div class="sidebar-icon" data-panel="history">
                    <i class="fas fa-history"></i>
                    <div class="sidebar-tooltip">Tracking History</div>
                </div>
            </div>

            <!-- Bottom Icons -->
            <div class="sidebar-bottom">
                <div class="sidebar-icon" data-panel="help">
                    <i class="fas fa-question-circle"></i>
                    <div class="sidebar-tooltip">Help & Support</div>
                </div>

                <div class="sidebar-icon" onclick="window.location.href='/'">
                    <i class="fas fa-home"></i>
                    <div class="sidebar-tooltip">Back to Home</div>
                </div>
            </div>
        </div>

        <!-- Sliding Panels -->
        <div class="sidebar-panel" id="tracking-panel">
            <div class="panel-header">
                <h3>Track Shipment</h3>
                <button class="panel-close" onclick="closePanel()">&times;</button>
            </div>
            <div class="panel-content">
                <p>Enter tracking number to search for shipments...</p>
            </div>
        </div>

        <div class="sidebar-panel" id="documents-panel">
            <div class="panel-header">
                <h3>Documents</h3>
                <button class="panel-close" onclick="closePanel()">&times;</button>
            </div>
            <div class="panel-content">
                <p>Document requests and uploads will appear here...</p>
            </div>
        </div>

        <div class="sidebar-panel" id="notifications-panel">
            <div class="panel-header">
                <h3>Notifications</h3>
                <button class="panel-close" onclick="closePanel()">&times;</button>
            </div>
            <div class="panel-content">
                <p>Recent notifications and updates...</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="demo-content">
            <h1>New Track Page Sidebar Demo</h1>
            <p>This is a demonstration of the new icon-based sidebar design for the track page. Click on the icons in the sidebar to see the sliding panels in action.</p>
            
            <div class="demo-features">
                <div class="feature-card">
                    <i class="fas fa-mouse-pointer"></i>
                    <h4>Interactive Icons</h4>
                    <p>Hover over icons to see tooltips and click to open panels</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-bell"></i>
                    <h4>Notification Badges</h4>
                    <p>Red badges show unread counts for documents and notifications</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-expand-arrows-alt"></i>
                    <h4>Sliding Panels</h4>
                    <p>Panels slide out from the sidebar with smooth animations</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-palette"></i>
                    <h4>Modern Design</h4>
                    <p>Clean, modern interface with gradient effects and shadows</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Demo JavaScript for sidebar functionality
        document.querySelectorAll('.sidebar-icon[data-panel]').forEach(icon => {
            icon.addEventListener('click', function() {
                const panelId = this.getAttribute('data-panel') + '-panel';
                const panel = document.getElementById(panelId);
                
                // Close all panels
                document.querySelectorAll('.sidebar-panel').forEach(p => {
                    p.classList.remove('active');
                });
                
                // Remove active class from all icons
                document.querySelectorAll('.sidebar-icon').forEach(i => {
                    i.classList.remove('active');
                });
                
                // Open selected panel and activate icon
                if (panel) {
                    panel.classList.add('active');
                    this.classList.add('active');
                }
            });
        });

        function closePanel() {
            document.querySelectorAll('.sidebar-panel').forEach(p => {
                p.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-icon').forEach(i => {
                i.classList.remove('active');
            });
        }

        // Close panel when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.new-sidebar') && !e.target.closest('.sidebar-panel')) {
                closePanel();
            }
        });
    </script>
</body>
</html>
