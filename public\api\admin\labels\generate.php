<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;
use App\Core\View;

// Initialize the application
$app = App::getInstance();

// Set appropriate headers
if (isset($_POST['download']) && $_POST['download'] === '1') {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="shipping-label.pdf"');
} else {
    header('Content-Type: application/json');
}

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    if (!isset($_POST['download'])) {
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    }
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get form data
        $shipment_id = $_POST['shipment_id'] ?? null;
        $label_design = $_POST['label_design'] ?? 'classic';
        $label_type = $_POST['label_type'] ?? 'standard';
        $label_size = $_POST['label_size'] ?? '4x6';
        $include_logo = isset($_POST['include_logo']) ? true : false;
        $include_barcode = isset($_POST['include_barcode']) ? true : false;
        $include_return_address = isset($_POST['include_return_address']) ? true : false;
        
        if (!$shipment_id) {
            if (isset($_POST['download'])) {
                http_response_code(400);
                echo "Shipment ID is required";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
                exit;
            }
        }
        
        // Get shipment data
        Database::prepare("SELECT * FROM shipments WHERE id = :id");
        Database::bindValue(':id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();
        
        if (!$shipment) {
            if (isset($_POST['download'])) {
                http_response_code(404);
                echo "Shipment not found";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment not found']);
                exit;
            }
        }
        
        // Generate label HTML
        $labelHtml = generateLabelHtml($shipment, [
            'label_design' => $label_design,
            'label_type' => $label_type,
            'label_size' => $label_size,
            'include_logo' => $include_logo,
            'include_barcode' => $include_barcode,
            'include_return_address' => $include_return_address
        ]);
        
        if (isset($_POST['download']) && $_POST['download'] === '1') {
            // Generate PDF and return as download
            $pdf = generateLabelPdf($labelHtml, $shipment);
            echo $pdf;
        } else {
            // Return HTML for preview
            echo json_encode([
                'success' => true,
                'html' => $labelHtml,
                'shipment_id' => $shipment_id,
                'tracking_number' => $shipment['tracking_number']
            ]);
        }
        
    } else {
        http_response_code(405);
        if (!isset($_POST['download'])) {
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        }
    }
    
} catch (Exception $e) {
    error_log("Error in label generation API: " . $e->getMessage());
    http_response_code(500);
    if (isset($_POST['download'])) {
        echo "Error generating label: " . $e->getMessage();
    } else {
        echo json_encode(['success' => false, 'message' => 'Error generating label: ' . $e->getMessage()]);
    }
}

function generateLabelHtml($shipment, $options) {
    $label_design = $options['label_design'] ?? 'classic';

    // Route to appropriate design function
    switch ($label_design) {
        case 'modern':
            return generateModernLabelHtml($shipment, $options);
        case 'minimal':
            return generateMinimalLabelHtml($shipment, $options);
        case 'classic':
        default:
            return generateClassicLabelHtml($shipment, $options);
    }
}

function generateClassicLabelHtml($shipment, $options) {
    // Get current date and time
    $currentDate = date('Y-m-d');
    $currentTime = date('H:i');

    // Get appearance settings
    $appearanceSettings = [];
    try {
        Database::prepare("SELECT name, value FROM settings WHERE category = 'appearance'");
        Database::execute();
        $results = Database::fetchAll();

        foreach ($results as $setting) {
            $appearanceSettings[$setting['name']] = $setting['value'];
        }
    } catch (Exception $e) {
        // Use defaults if settings not found
    }

    // Set default colors if not found in settings
    $primaryColor = $appearanceSettings['primary_color'] ?? '#2c3e50';
    $secondaryColor = $appearanceSettings['secondary_color'] ?? '#e74c3c';
    $accentColor = $appearanceSettings['accent_color'] ?? '#3498db';

    // Get company logo from settings
    $logoHtml = '';
    if (!empty($appearanceSettings['company_logo'])) {
        $logoPath = $appearanceSettings['company_logo'];
        $logoUrl = $logoPath;
        $logoHtml = '<img src="' . $logoUrl . '" alt="Company Logo" style="max-height: 50px; max-width: 150px; margin-bottom: 5px;">';
    }

    $html = '
    <div class="shipping-label" style="width: 600px; min-height: 800px; border: 3px solid ' . $primaryColor . '; padding: 0; font-family: Arial, sans-serif; background: white; margin: 0 auto;">
        <!-- Header Section -->
        <div class="label-header" style="background: ' . $primaryColor . '; color: white; padding: 15px 20px; border-bottom: 3px solid #000; display: flex; justify-content: space-between; align-items: center;">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 28px; font-weight: bold; color: white; margin-bottom: 5px;">
                    <span style="background: ' . $secondaryColor . '; color: white; padding: 5px 10px; border-radius: 3px;">ELTA</span>
                    <span style="color: white; margin-left: 5px;">COURIER</span>
                </div>
                ') . '
                <div style="font-size: 12px; color: rgba(255,255,255,0.8);">The Bridge To Your Logistics Success</div>
            </div>
            ' . ($options['include_barcode'] ? '
            <div class="barcode-section" style="text-align: center;">
                <div class="barcode-container" style="margin: 10px 0;">
                    ' . generateBarcodeImage($shipment['tracking_number']) . '
                </div>
                <div class="tracking-number" style="font-size: 14px; font-weight: bold; margin-top: 5px; color: ' . $primaryColor . ';">
                    ' . htmlspecialchars($shipment['tracking_number']) . '
                </div>
            </div>
            ' : '') . '
        </div>

        <!-- Shipper and Receiver Details Section -->
        <div class="addresses-section" style="display: flex; border-bottom: 3px solid #000; border-top: 2px solid #000;">
            <!-- Shipper Details -->
            <div class="shipper-section" style="width: 50%; padding: 20px; border-right: 3px solid #000;">
                <div class="section-title" style="background: #2c3e50; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px; border-bottom: 1px solid #000; text-align: left;">
                    SHIPPER DETAILS
                </div>
                <div class="shipper-info" style="font-size: 12px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Shipper Name:</strong> ' . htmlspecialchars($shipment['shipper_name'] ?? 'ELTA Courier Services') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Phone Number:</strong> ' . htmlspecialchars($shipment['shipper_phone'] ?? '+30 210 1234567') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Address:</strong></div>
                    <div style="margin-left: 10px; margin-bottom: 8px;">' . nl2br(htmlspecialchars($shipment['shipper_address'] ?? 'Athens, Greece<br>Postal Code: 10431')) . '</div>
                    <div style="margin-bottom: 8px;"><strong>Email:</strong> ' . htmlspecialchars($shipment['shipper_email'] ?? '<EMAIL>') . '</div>
                </div>
            </div>

            <!-- Receiver Details -->
            <div class="receiver-section" style="width: 50%; padding: 20px;">
                <div class="section-title" style="background: #e74c3c; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px; border-bottom: 1px solid #000; text-align: left;">
                    RECEIVER DETAILS
                </div>
                <div class="receiver-info" style="font-size: 12px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Receiver Name:</strong> ' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Phone Number:</strong> ' . htmlspecialchars($shipment['receiver_phone'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Address:</strong></div>
                    <div style="margin-left: 10px; margin-bottom: 8px;">' . nl2br(htmlspecialchars($shipment['receiver_address'] ?? 'N/A')) . '</div>
                    <div style="margin-bottom: 8px;"><strong>Email:</strong> ' . htmlspecialchars($shipment['receiver_email'] ?? 'N/A') . '</div>
                </div>
            </div>
        </div>

        <!-- Package Details Section -->
        <div class="package-section" style="padding: 20px; border-bottom: 3px solid #000; border-top: 2px solid #000;">
            <div class="section-title" style="background: #27ae60; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px; border-bottom: 1px solid #000;">
                PACKAGE DETAILS
            </div>
            <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Qty</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Piece Type</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Description</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Length(cm)</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Width(cm)</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Height(cm)</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Weight (kg)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">1</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">Parcel</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">' . htmlspecialchars($shipment['items'] ?? 'General Merchandise') . '</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">' . htmlspecialchars($shipment['total_weight'] ?? '1.0') . '</td>
                    </tr>
                </tbody>
            </table>
            <div style="margin-top: 15px; display: flex; justify-content: space-between; font-size: 12px;">
                <div><strong>Total Volumetric Weight:</strong> ' . htmlspecialchars($shipment['total_weight'] ?? '1.0') . ' kg</div>
                <div><strong>Total Volume:</strong> 0.001 cu. m.</div>
                <div><strong>Total Actual Weight:</strong> ' . htmlspecialchars($shipment['total_weight'] ?? '1.0') . ' kg</div>
            </div>
        </div>

        <!-- Service Information -->
        <div class="service-section" style="padding: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-size: 12px; border-bottom: 1px solid #000; border-top: 1px solid #000;">
            <div class="section-title" style="background: #17a2b8; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px; border-bottom: 1px solid #000; text-align: left;">
                PICKUP & DELIVERY INFORMATION
            </div>

            <!-- Pickup & Delivery Details -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; padding: 15px; background: white; border-radius: 6px; border: 1px solid #dee2e6;">
                <div style="padding: 10px;">
                    <div style="margin-bottom: 8px; color: #17a2b8; font-weight: bold; font-size: 13px;">📅 PICKUP DETAILS</div>
                    <div style="margin-bottom: 6px;"><strong>Date:</strong> ' . $currentDate . '</div>
                    <div style="margin-bottom: 6px;"><strong>Time:</strong> ' . $currentTime . '</div>
                    <div><strong>Origin:</strong> ' . htmlspecialchars($shipment['origin'] ?? 'Athens, Greece') . '</div>
                </div>
                <div style="padding: 10px;">
                    <div style="margin-bottom: 8px; color: #28a745; font-weight: bold; font-size: 13px;">🚚 DELIVERY DETAILS</div>
                    <div style="margin-bottom: 6px;"><strong>Date:</strong> ' . ($shipment['expected_delivery_date'] ? date('Y-m-d', strtotime($shipment['expected_delivery_date'])) : date('Y-m-d', strtotime('+2 days'))) . '</div>
                    <div style="margin-bottom: 6px;"><strong>Departure:</strong> ' . date('H:i', strtotime('+1 hour')) . '</div>
                    <div><strong>Destination:</strong> ' . htmlspecialchars($shipment['destination'] ?? 'N/A') . '</div>
                </div>
            </div>

            <!-- Service Details -->
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; padding: 15px; background: white; border-radius: 6px; border: 1px solid #dee2e6;">
                <div style="text-align: center; padding: 10px;">
                    <div style="color: #6c757d; font-size: 11px; margin-bottom: 4px;">SERVICE</div>
                    <div style="font-weight: bold; color: #495057;">ELTA COURIER SERVICE</div>
                </div>
                <div style="text-align: center; padding: 10px;">
                    <div style="color: #6c757d; font-size: 11px; margin-bottom: 4px;">CARRIER</div>
                    <div style="font-weight: bold; color: #495057;">ELTA</div>
                </div>
                <div style="text-align: center; padding: 10px;">
                    <div style="color: #6c757d; font-size: 11px; margin-bottom: 4px;">REFERENCE NO</div>
                    <div style="font-weight: bold; color: #495057;">' . htmlspecialchars($shipment['tracking_number']) . '</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-section" style="padding: 10px 20px; border-top: 2px solid #000; font-size: 10px; color: #666; text-align: center;">
            <div>Thank you for choosing ELTA Courier - Version 6.5.1</div>
        </div>
    </div>';

    return $html;
}

function generateModernLabelHtml($shipment, $options) {
    // Get appearance settings
    $appearanceSettings = [];
    try {
        Database::prepare("SELECT name, value FROM settings WHERE category = 'appearance'");
        Database::execute();
        $results = Database::fetchAll();

        foreach ($results as $setting) {
            $appearanceSettings[$setting['name']] = $setting['value'];
        }
    } catch (Exception $e) {
        // Use defaults if settings not found
    }

    $primaryColor = $appearanceSettings['primary_color'] ?? '#2c3e50';
    $secondaryColor = $appearanceSettings['secondary_color'] ?? '#3498db';
    $logoPath = $appearanceSettings['company_logo'] ?? '';

    // Logo HTML
    $logoHtml = '';
    if ($options['include_logo'] && !empty($logoPath)) {
        $logoHtml = '<img src="' . $logoPath . '" alt="Company Logo" style="max-height: 40px; max-width: 120px; object-fit: contain;">';
    }

    $html = '
    <div class="shipping-label modern-design" style="width: 600px; min-height: 800px; border: 2px solid ' . $primaryColor . '; font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif; background: white; margin: 0 auto; overflow: hidden;">
        <!-- Header Section -->
        <div class="label-header" style="background: linear-gradient(135deg, ' . $primaryColor . ' 0%, ' . $secondaryColor . ' 100%); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center;">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 24px; font-weight: 300; color: white; margin-bottom: 5px;">
                    <span style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; border-radius: 4px; font-weight: 600;">ELTA</span>
                </div>') . '
                <div style="font-size: 12px; opacity: 0.9; margin-top: 5px;">Modern Courier Services</div>
            </div>
            ' . ($options['include_barcode'] ? '
            <div class="barcode-section" style="text-align: center;">
                <div class="barcode-container" style="margin: 10px 0;">
                    ' . generateBarcodeImage($shipment['tracking_number']) . '
                </div>
                <div class="tracking-number" style="font-size: 12px; font-weight: 600; margin-top: 5px; background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 3px;">
                    ' . htmlspecialchars($shipment['tracking_number']) . '
                </div>
            </div>
            ' : '') . '
        </div>

        <!-- Main Content Grid -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0; height: calc(100% - 80px);">
            <!-- From Section -->
            <div style="padding: 20px; border-right: 1px solid #e1e5e9;">
                <h3 style="margin: 0 0 15px 0; color: ' . $primaryColor . '; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 1px; border-bottom: 2px solid ' . $primaryColor . '; padding-bottom: 5px;">From</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 3px solid ' . $secondaryColor . ';">
                    <div style="font-weight: 600; margin-bottom: 8px; color: #2c3e50;">' . htmlspecialchars($shipment['sender_name'] ?? $shipment['shipper_name'] ?? 'N/A') . '</div>
                    <div style="color: #6c757d; line-height: 1.4; font-size: 13px;">
                        ' . htmlspecialchars($shipment['sender_address'] ?? $shipment['shipper_address'] ?? 'N/A') . '<br>
                        ' . htmlspecialchars($shipment['sender_city'] ?? $shipment['shipper_city'] ?? '') . ', ' . htmlspecialchars($shipment['sender_country'] ?? $shipment['shipper_country'] ?? '') . '<br>
                        <strong>Phone:</strong> ' . htmlspecialchars($shipment['sender_phone'] ?? $shipment['shipper_phone'] ?? '') . '
                    </div>
                </div>
            </div>

            <!-- To Section -->
            <div style="padding: 20px;">
                <h3 style="margin: 0 0 15px 0; color: ' . $primaryColor . '; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 1px; border-bottom: 2px solid ' . $primaryColor . '; padding-bottom: 5px;">To</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 3px solid ' . $secondaryColor . ';">
                    <div style="font-weight: 600; margin-bottom: 8px; color: #2c3e50;">' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</div>
                    <div style="color: #6c757d; line-height: 1.4; font-size: 13px;">
                        ' . htmlspecialchars($shipment['receiver_address'] ?? 'N/A') . '<br>
                        ' . htmlspecialchars($shipment['receiver_city'] ?? '') . ', ' . htmlspecialchars($shipment['receiver_country'] ?? '') . '<br>
                        <strong>Phone:</strong> ' . htmlspecialchars($shipment['receiver_phone'] ?? '') . '
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Info Bar -->
        <div style="background: #f8f9fa; padding: 15px 20px; border-top: 1px solid #e1e5e9; display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; gap: 30px;">
                <div>
                    <div style="color: #6c757d; font-size: 11px; margin-bottom: 2px; text-transform: uppercase;">Service</div>
                    <div style="font-weight: 600; color: #2c3e50; font-size: 13px;">' . htmlspecialchars($shipment['service_type'] ?? 'Standard') . '</div>
                </div>
                <div>
                    <div style="color: #6c757d; font-size: 11px; margin-bottom: 2px; text-transform: uppercase;">Weight</div>
                    <div style="font-weight: 600; color: #2c3e50; font-size: 13px;">' . htmlspecialchars($shipment['weight'] ?? 'N/A') . ' kg</div>
                </div>
                <div>
                    <div style="color: #6c757d; font-size: 11px; margin-bottom: 2px; text-transform: uppercase;">Date</div>
                    <div style="font-weight: 600; color: #2c3e50; font-size: 13px;">' . date('M j, Y') . '</div>
                </div>
            </div>
            <div style="text-align: right;">
                <div style="color: #6c757d; font-size: 11px; margin-bottom: 2px; text-transform: uppercase;">Tracking</div>
                <div style="font-weight: 700; color: ' . $primaryColor . '; font-size: 14px; font-family: monospace;">' . htmlspecialchars($shipment['tracking_number']) . '</div>
            </div>
        </div>
    </div>';

    return $html;
}

function generateMinimalLabelHtml($shipment, $options) {
    // Get appearance settings
    $appearanceSettings = [];
    try {
        Database::prepare("SELECT name, value FROM settings WHERE category = 'appearance'");
        Database::execute();
        $results = Database::fetchAll();

        foreach ($results as $setting) {
            $appearanceSettings[$setting['name']] = $setting['value'];
        }
    } catch (Exception $e) {
        // Use defaults if settings not found
    }

    $primaryColor = $appearanceSettings['primary_color'] ?? '#000000';
    $logoPath = $appearanceSettings['company_logo'] ?? '';

    // Logo HTML
    $logoHtml = '';
    if ($options['include_logo'] && !empty($logoPath)) {
        $logoHtml = '<img src="' . $logoPath . '" alt="Company Logo" style="max-height: 35px; max-width: 100px; object-fit: contain;">';
    }

    $html = '
    <div class="shipping-label minimal-design" style="width: 600px; min-height: 800px; border: 1px solid #000; font-family: \'Helvetica Neue\', Arial, sans-serif; background: white; margin: 0 auto;">
        <!-- Header Section -->
        <div class="label-header" style="padding: 20px; border-bottom: 2px solid #000; display: flex; justify-content: space-between; align-items: center;">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 20px; font-weight: 700; color: #000; margin-bottom: 3px;">ELTA COURIER</div>') . '
                <div style="font-size: 10px; color: #666; margin-top: 3px;">Professional Shipping Services</div>
            </div>
            ' . ($options['include_barcode'] ? '
            <div class="barcode-section" style="text-align: center;">
                <div class="barcode-container" style="margin: 5px 0;">
                    ' . generateBarcodeImage($shipment['tracking_number']) . '
                </div>
                <div class="tracking-number" style="font-size: 11px; font-weight: 600; margin-top: 3px; color: #000;">
                    ' . htmlspecialchars($shipment['tracking_number']) . '
                </div>
            </div>
            ' : '') . '
        </div>

        <!-- Address Sections -->
        <div style="padding: 0;">
            <!-- From Section -->
            <div style="padding: 20px; border-bottom: 1px solid #ccc;">
                <h3 style="margin: 0 0 10px 0; font-size: 12px; font-weight: 700; color: #000; text-transform: uppercase; letter-spacing: 1px;">FROM</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 5px 0; font-weight: 600; color: #000; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['sender_name'] ?? $shipment['shipper_name'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; color: #333; border-bottom: 1px solid #eee; font-size: 13px;">' . htmlspecialchars($shipment['sender_address'] ?? $shipment['shipper_address'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; color: #333; border-bottom: 1px solid #eee; font-size: 13px;">' . htmlspecialchars($shipment['sender_city'] ?? $shipment['shipper_city'] ?? '') . ', ' . htmlspecialchars($shipment['sender_country'] ?? $shipment['shipper_country'] ?? '') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; color: #333; font-size: 13px;">' . htmlspecialchars($shipment['sender_phone'] ?? $shipment['shipper_phone'] ?? '') . '</td>
                    </tr>
                </table>
            </div>

            <!-- To Section -->
            <div style="padding: 20px; border-bottom: 1px solid #ccc;">
                <h3 style="margin: 0 0 10px 0; font-size: 12px; font-weight: 700; color: #000; text-transform: uppercase; letter-spacing: 1px;">TO</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 5px 0; font-weight: 600; color: #000; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; color: #333; border-bottom: 1px solid #eee; font-size: 13px;">' . htmlspecialchars($shipment['receiver_address'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; color: #333; border-bottom: 1px solid #eee; font-size: 13px;">' . htmlspecialchars($shipment['receiver_city'] ?? '') . ', ' . htmlspecialchars($shipment['receiver_country'] ?? '') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; color: #333; font-size: 13px;">' . htmlspecialchars($shipment['receiver_phone'] ?? '') . '</td>
                    </tr>
                </table>
            </div>

            <!-- Service Information -->
            <div style="padding: 15px 20px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #000; width: 120px; border-bottom: 1px solid #eee;">Service:</td>
                        <td style="padding: 8px 0; color: #333; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['service_type'] ?? 'Standard') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #000; border-bottom: 1px solid #eee;">Weight:</td>
                        <td style="padding: 8px 0; color: #333; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['weight'] ?? 'N/A') . ' kg</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #000; border-bottom: 1px solid #eee;">Date:</td>
                        <td style="padding: 8px 0; color: #333; border-bottom: 1px solid #eee;">' . date('Y-m-d H:i') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 700; color: #000;">Tracking:</td>
                        <td style="padding: 8px 0; font-weight: 700; color: #000; font-family: monospace;">' . htmlspecialchars($shipment['tracking_number']) . '</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>';

    return $html;
}

function generateLabelPdf($html, $shipment) {
    // For now, return a simple PDF placeholder
    // In a real implementation, you would use a library like TCPDF or DomPDF
    $pdf_content = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Shipping Label - " . $shipment['tracking_number'] . ") Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF";
    
    return $pdf_content;
}

function generateBarcodeImage($text) {
    try {
        // Use the barcode generator library
        $generator = new \Picqer\Barcode\BarcodeGeneratorHTML();
        $barcode = $generator->getBarcode($text, $generator::TYPE_CODE_128, 2, 50);

        return '<div style="border: 1px solid #ddd; padding: 5px; background: white; display: inline-block;">' . $barcode . '</div>';
    } catch (Exception $e) {
        // Fallback to simple text representation
        return '<div style="border: 1px solid #ddd; padding: 10px; background: white; font-family: monospace; font-size: 12px; text-align: center;">' . htmlspecialchars($text) . '</div>';
    }
}
?>
