<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $request_id = $_POST['request_id'] ?? '';
        if (empty($request_id)) {
            echo json_encode(['success' => false, 'message' => 'Request ID is required']);
            exit;
        }
        // Update document request status to cancelled
        $stmt = $pdo->prepare('UPDATE document_requests SET status = ? WHERE id = ?');
        $stmt->execute(['cancelled', $request_id]);
        echo json_encode(['success' => true, 'message' => 'Document request cancelled successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
} catch (PDOException $e) {
    error_log("Database error in cancel-document-request.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in cancel-document-request.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred']);
}
?>
