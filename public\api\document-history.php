<?php
header('Content-Type: application/json');

try {
    // Database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Only GET requests are allowed');
    }

    $requestId = $_GET['request_id'] ?? null;

    if (!$requestId) {
        throw new Exception('Request ID is required');
    }

    // Get document request history
    $stmt = $pdo->prepare("
        SELECT
            dr.id,
            dr.status,
            dr.created_at,
            dr.updated_at,
            dr.request_message,
            dr.due_date,
            u.name as user_name,
            dt.name as document_type_name,
            CASE
                WHEN dr.status = 'pending' THEN 'Document Requested'
                WHEN dr.status = 'uploaded' THEN 'Document Uploaded'
                WHEN dr.status = 'approved' THEN 'Document Approved'
                WHEN dr.status = 'rejected' THEN 'Document Rejected'
                WHEN dr.status = 'cancelled' THEN 'Request Cancelled'
                ELSE dr.status
            END as status_display
        FROM document_requests dr
        LEFT JOIN users u ON dr.requested_by = u.id
        LEFT JOIN document_types dt ON dr.document_type_id = dt.id
        WHERE dr.id = ?
        ORDER BY dr.created_at DESC
    ");

    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$request) {
        throw new Exception('Document request not found');
    }

    // Get document uploads for this request
    $stmt = $pdo->prepare("
        SELECT
            du.id,
            du.original_filename,
            du.file_size,
            du.uploaded_at,
            du.upload_notes
        FROM document_uploads du
        WHERE du.document_request_id = ?
        ORDER BY du.uploaded_at DESC
    ");

    $stmt->execute([$requestId]);
    $uploads = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get document approvals (reviews)
    $stmt = $pdo->prepare("
        SELECT
            da.id,
            da.status,
            da.review_notes,
            da.reviewed_at,
            u.name as reviewer_name
        FROM document_approvals da
        LEFT JOIN users u ON da.reviewed_by = u.id
        WHERE da.document_request_id = ?
        ORDER BY da.reviewed_at DESC
    ");

    $stmt->execute([$requestId]);
    $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Build comprehensive history
    $history = [];

    // Add initial request
    $history[] = [
        'status' => 'pending',
        'status_display' => 'Document Requested',
        'created_at' => $request['created_at'],
        'created_at_formatted' => date('M j, Y g:i A', strtotime($request['created_at'])),
        'notes' => "Document type: {$request['document_type_name']}" . 
                  ($request['due_date'] ? " | Due: " . date('M j, Y', strtotime($request['due_date'])) : ''),
        'user_name' => $request['user_name']
    ];

    // Add uploads
    foreach ($uploads as $upload) {
        $history[] = [
            'status' => 'uploaded',
            'status_display' => 'Document Uploaded',
            'created_at' => $upload['uploaded_at'],
            'created_at_formatted' => date('M j, Y g:i A', strtotime($upload['uploaded_at'])),
            'notes' => "File: {$upload['original_filename']}" .
                      ($upload['upload_notes'] ? " | Notes: {$upload['upload_notes']}" : ''),
            'user_name' => 'System', // Since we don't have uploaded_by in the uploads table
            'upload_id' => $upload['id'] // Add upload ID for preview functionality
        ];
    }

    // Add reviews
    foreach ($reviews as $review) {
        $history[] = [
            'status' => $review['status'],
            'status_display' => $review['status'] === 'approved' ? 'Document Approved' : 'Document Rejected',
            'created_at' => $review['reviewed_at'],
            'created_at_formatted' => date('M j, Y g:i A', strtotime($review['reviewed_at'])),
            'notes' => $review['review_notes'],
            'user_name' => $review['reviewer_name']
        ];
    }

    // Sort history by date (newest first)
    usort($history, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    echo json_encode([
        'success' => true,
        'history' => $history,
        'request' => $request
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
