<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 2));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\Session;

header('Content-Type: application/json');

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access. Admin login required.']);
    exit;
}

try {
    // Database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $uploadId = $_GET['upload_id'] ?? '';
        
        if (empty($uploadId)) {
            echo json_encode(['success' => false, 'message' => 'Upload ID is required']);
            exit;
        }
        
        // Get document upload information
        $stmt = $pdo->prepare('
            SELECT du.*, dr.id as document_request_id, dr.shipment_id, dr.document_type_id, dt.name as document_type_name
            FROM document_uploads du
            JOIN document_requests dr ON du.document_request_id = dr.id
            JOIN document_types dt ON dr.document_type_id = dt.id
            WHERE du.id = ?
        ');
        $stmt->execute([$uploadId]);
        $upload = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$upload) {
            echo json_encode(['success' => false, 'message' => 'Document not found']);
            exit;
        }
        
        // Format the uploaded date
        $upload['uploaded_at_formatted'] = date('M j, Y g:i A', strtotime($upload['uploaded_at']));
        
        echo json_encode([
            'success' => true,
            'upload' => $upload
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Error in document preview API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error loading document: ' . $e->getMessage()]);
}
?>