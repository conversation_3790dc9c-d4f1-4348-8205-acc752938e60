<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Get tracking number from query parameter
$trackingNumber = $_GET['tracking_number'] ?? '';

if (empty($trackingNumber)) {
    echo json_encode([
        'success' => false,
        'error' => 'Tracking number is required'
    ]);
    exit;
}

// Sample document requests data for testing
$sampleDocuments = [
    'TRK123456789' => [
        [
            'id' => 1,
            'document_type_name' => 'Commercial Invoice',
            'status' => 'pending',
            'priority' => 'high',
            'request_message' => 'Please upload the commercial invoice for customs clearance. This document is required for international shipments.',
            'created_at' => '2024-01-15 10:30:00',
            'due_date' => '2024-01-25 23:59:59'
        ],
        [
            'id' => 2,
            'document_type_name' => 'Packing List',
            'status' => 'pending',
            'priority' => 'medium',
            'request_message' => 'Detailed packing list required for inspection by customs authorities.',
            'created_at' => '2024-01-16 14:20:00',
            'due_date' => '2024-01-28 23:59:59'
        ],
        [
            'id' => 3,
            'document_type_name' => 'Certificate of Origin',
            'status' => 'cancelled',
            'priority' => 'low',
            'request_message' => 'Certificate of origin was requested but later cancelled due to shipment route change.',
            'created_at' => '2024-01-10 09:15:00',
            'cancelled_at' => '2024-01-12 16:30:00'
        ],
        [
            'id' => 4,
            'document_type_name' => 'Bill of Lading',
            'status' => 'approved',
            'priority' => 'high',
            'request_message' => 'Bill of lading has been reviewed and approved by our logistics team.',
            'created_at' => '2024-01-08 11:45:00',
            'approved_at' => '2024-01-14 13:20:00',
            'file_path' => '/documents/bill-of-lading-123.pdf'
        ],
        [
            'id' => 5,
            'document_type_name' => 'Insurance Certificate',
            'status' => 'approved',
            'priority' => 'medium',
            'request_message' => 'Insurance certificate approved for shipment coverage.',
            'created_at' => '2024-01-09 15:30:00',
            'approved_at' => '2024-01-15 10:15:00',
            'file_path' => '/documents/insurance-cert-456.pdf'
        ],
        [
            'id' => 6,
            'document_type_name' => 'Export License',
            'status' => 'requested',
            'priority' => 'high',
            'request_message' => 'Export license required for controlled goods. Please upload the valid export license.',
            'created_at' => '2024-01-17 08:00:00',
            'due_date' => '2024-01-30 23:59:59'
        ]
    ],
    'TRK987654321' => [
        [
            'id' => 7,
            'document_type_name' => 'Customs Declaration',
            'status' => 'pending',
            'priority' => 'high',
            'request_message' => 'Customs declaration form needs to be completed and uploaded.',
            'created_at' => '2024-01-18 12:00:00',
            'due_date' => '2024-01-22 23:59:59'
        ]
    ]
];

// Get documents for the requested tracking number
$documents = $sampleDocuments[$trackingNumber] ?? [];

if (empty($documents)) {
    echo json_encode([
        'success' => true,
        'requests' => [],
        'message' => 'No document requests found for this tracking number'
    ]);
} else {
    echo json_encode([
        'success' => true,
        'requests' => $documents,
        'total_count' => count($documents),
        'pending_count' => count(array_filter($documents, function($doc) {
            return in_array($doc['status'], ['pending', 'requested']);
        })),
        'approved_count' => count(array_filter($documents, function($doc) {
            return in_array($doc['status'], ['approved', 'uploaded']);
        })),
        'cancelled_count' => count(array_filter($documents, function($doc) {
            return $doc['status'] === 'cancelled';
        }))
    ]);
}
?>