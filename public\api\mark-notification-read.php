<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['notification_id'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Notification ID is required'
    ]);
    exit;
}

$notificationId = $input['notification_id'];

// For this demo, we'll just return success
// In a real application, you would:
// 1. Validate the notification belongs to the current user
// 2. Update the database to mark the notification as read
// 3. Return the updated status

echo json_encode([
    'success' => true,
    'message' => 'Notification marked as read',
    'notification_id' => $notificationId
]);
?>