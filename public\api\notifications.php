<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Get tracking number from query parameter
$trackingNumber = $_GET['tracking_number'] ?? '';

if (empty($trackingNumber)) {
    echo json_encode([
        'success' => false,
        'error' => 'Tracking number is required'
    ]);
    exit;
}

// Sample notifications data for testing
$sampleNotifications = [
    'TRK123456789' => [
        [
            'id' => 1,
            'title' => 'Shipment Picked Up',
            'message' => 'Your shipment has been picked up from the sender and is now in transit.',
            'type' => 'status_update',
            'is_read' => false,
            'created_at' => '2024-01-18 14:30:00',
            'action_url' => null
        ],
        [
            'id' => 2,
            'title' => 'Document Request',
            'message' => 'Commercial invoice is required for customs clearance. Please upload the document.',
            'type' => 'document_request',
            'is_read' => false,
            'created_at' => '2024-01-18 10:15:00',
            'action_url' => '#documents'
        ],
        [
            'id' => 3,
            'title' => 'Shipment Delayed',
            'message' => 'Your shipment has been delayed due to weather conditions. Expected delivery: Jan 25, 2024.',
            'type' => 'delay',
            'is_read' => false,
            'created_at' => '2024-01-17 16:45:00',
            'action_url' => null
        ],
        [
            'id' => 4,
            'title' => 'Customs Clearance',
            'message' => 'Your shipment is currently undergoing customs clearance procedures.',
            'type' => 'status_update',
            'is_read' => true,
            'created_at' => '2024-01-17 09:20:00',
            'action_url' => null
        ],
        [
            'id' => 5,
            'title' => 'Document Approved',
            'message' => 'Your bill of lading has been reviewed and approved.',
            'type' => 'document_request',
            'is_read' => true,
            'created_at' => '2024-01-16 13:10:00',
            'action_url' => '#documents'
        ]
    ],
    'TRK987654321' => [
        [
            'id' => 6,
            'title' => 'Shipment Created',
            'message' => 'Your shipment has been created and is awaiting pickup.',
            'type' => 'status_update',
            'is_read' => false,
            'created_at' => '2024-01-18 11:00:00',
            'action_url' => null
        ]
    ]
];

// Get notifications for the requested tracking number
$notifications = $sampleNotifications[$trackingNumber] ?? [];

if (empty($notifications)) {
    echo json_encode([
        'success' => true,
        'notifications' => [],
        'message' => 'No notifications found for this tracking number'
    ]);
} else {
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'total_count' => count($notifications),
        'unread_count' => count(array_filter($notifications, function($notif) {
            return !$notif['is_read'];
        }))
    ]);
}
?>