/* Admin Panel CSS - Tailwind-inspired */

/* Base Styles */
:root {
    --primary-color: #333333;
    --secondary-color: #555555;
    --success-color: #444444;
    --danger-color: #666666;
    --warning-color: #888888;
    --info-color: #555555;
    --light-color: #f5f5f5;
    --dark-color: #222222;
    --border-color: #e2e8f0;
    --text-color: #1a202c;
    --text-muted: #718096;
    --sidebar-width: 250px;
    --header-height: 60px;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    line-height: 1.5;
    color: var(--text-color);
    background-color: #f7fafc;
    min-height: 100vh;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s;
}

a:hover {
    color: #4a5568;
}

/* Layout */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: #000000;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    z-index: 1000;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.header h1 {
    font-size: 1.25rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info button {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.875rem;
}

.user-info button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background-color: #ffffff;
    border-right: 1px solid #e2e8f0;
    overflow-y: auto;
    padding: 1.5rem 0;
    z-index: 900;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: 1.5rem;
    min-height: calc(100vh - var(--header-height));
}

/* Navigation */
nav ul {
    list-style: none;
}

nav ul li {
    margin-bottom: 0.25rem;
}

nav ul li a, nav ul li .menu-title {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #333333;
    text-decoration: none;
    transition: all 0.2s;
    border-left: 2px solid transparent;
    font-size: 0.875rem;
    cursor: pointer;
}

nav ul li a:hover,
nav ul li a.active,
nav ul li .menu-title:hover {
    background-color: #f5f5f5;
    border-left-color: #333333;
    color: #000000;
}

nav ul li a i,
nav ul li .menu-title i {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
    opacity: 0.75;
}

nav ul ul {
    margin-left: 1.25rem;
}

/* Cards */
.card {
    background-color: white;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8fafc;
    font-weight: 600;
    font-size: 0.875rem;
    color: #4a5568;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background-color: #f8fafc;
}

/* Dashboard Widgets */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stat-card .stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.stat-card .stat-value {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.stat-card .stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.stat-card.primary { border-top: 3px solid var(--primary-color); }
.stat-card.success { border-top: 3px solid var(--success-color); }
.stat-card.warning { border-top: 3px solid var(--warning-color); }
.stat-card.danger { border-top: 3px solid var(--danger-color); }

/* Tables */
.table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

table th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #4a5568;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

table tr:hover {
    background-color: #f8fafc;
}

table tr:last-child td {
    border-bottom: none;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: #4a5568;
}

.form-control {
    width: 100%;
    padding: 0.625rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    font-family: inherit;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background-color: white;
    color: var(--text-color);
}

.form-control:focus {
    outline: none;
    border-color: #a0aec0;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    line-height: 1.5;
    text-decoration: none;
}

.btn-primary {
    background-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-success {
    background-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    color: #212529;
}

.btn-info {
    background-color: #17a2b8;
}

.btn-info:hover {
    background-color: #138496;
}

.btn:hover {
    opacity: 0.9;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary { background-color: #333333; }
.btn-primary:hover { background-color: #555555; }

.btn-success { background-color: #444444; }
.btn-success:hover { background-color: #666666; }

.btn-danger { background-color: #666666; }
.btn-danger:hover { background-color: #888888; }

.btn-warning { background-color: #888888; }
.btn-warning:hover { background-color: #aaaaaa; }

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error, .alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.75em;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 9999px;
    color: white;
}

.bg-primary { background-color: #000000; }
.bg-success { background-color: #444444; }
.bg-danger { background-color: #666666; }
.bg-warning { background-color: #888888; }
.bg-info { background-color: #555555; }
.bg-secondary { background-color: #718096; }

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }

/* Text Utilities */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Shadow Utilities */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.d-flex { display: flex; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }

/* Row and Column Layout */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    overflow-y: auto;
    padding: 2rem 1rem;
}

.modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
    display: flex;
    align-items: center;
    min-height: calc(100% - 3.5rem);
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    outline: 0;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    margin-bottom: 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    gap: 0.5rem;
}

.close {
    background: transparent;
    border: 0;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
    cursor: pointer;
}

.close:hover {
    opacity: .75;
}

/* Responsive */
@media (max-width: 992px) {
    .sidebar {
        width: 200px;
    }

    .content {
        margin-left: 200px;
    }

    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .d-md-none {
        display: block;
    }
}

@media (min-width: 769px) {
    .d-md-none {
        display: none;
    }
}
