// Service Worker for ELTA Courier System
// This service worker provides basic caching functionality

const CACHE_NAME = 'elta-courier-v1';
const urlsToCache = [
    '/',
    '/css/admin.css',
    '/css/admin-tailwind.css',
    '/css/public-style.css',
    '/css/track-result.css',
    '/js/public-script.js',
    '/js/tracking-details.js'
];

// Install event - cache resources
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', function(event) {
    // Only handle GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip cross-origin requests
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Return cached version if available
                if (response) {
                    return response;
                }

                // Otherwise fetch from network with proper cache mode
                return fetch(event.request, {
                    mode: 'cors',
                    credentials: 'same-origin'
                });
            })
            .catch(function() {
                // Return a fallback response if both cache and network fail
                if (event.request.destination === 'document') {
                    return new Response('Offline - Please check your connection', {
                        status: 503,
                        statusText: 'Service Unavailable',
                        headers: { 'Content-Type': 'text/plain' }
                    });
                }
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});
