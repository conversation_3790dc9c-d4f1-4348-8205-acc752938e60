<?php
// Shipment view page
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Shipment Details</h1>
    <div>
        <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Shipments
        </a>
        <button type="button" class="btn btn-primary" onclick="openEditModal(<?= $shipment['id'] ?>)">
            <i class="fas fa-edit"></i> Edit Shipment
        </button>
        <a href="<?= App\Core\View::url('/admin/shipments/updateStatus/' . $shipment['id']) ?>" class="btn btn-info">
            <i class="fas fa-truck"></i> Update Status
        </a>
        <a href="<?= App\Core\View::url('/admin/shipments/' . $shipment['id'] . '/label') ?>" class="btn btn-warning" title="Generate Label">
            <i class="fas fa-tag"></i> Label
        </a>
        <a href="<?= App\Core\View::url('/admin/shipments/' . $shipment['id'] . '/invoice') ?>" class="btn btn-success" title="Generate Invoice">
            <i class="fas fa-file-invoice"></i> Invoice
        </a>
    </div>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">Shipment Information</div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>ID:</th>
                        <td><?= App\Core\View::e($shipment['id']) ?></td>
                    </tr>
                    <tr>
                        <th>Tracking Number:</th>
                        <td><?= App\Core\View::e($shipment['tracking_number']) ?></td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td>
                            <?php
                            $statusClass = 'bg-primary';
                            switch ($shipment['status']) {
                                case 'pending':
                                    $statusClass = 'bg-warning text-dark';
                                    break;
                                case 'in_transit':
                                    $statusClass = 'bg-info';
                                    break;
                                case 'out_for_delivery':
                                    $statusClass = 'bg-primary';
                                    break;
                                case 'delivered':
                                    $statusClass = 'bg-success';
                                    break;
                                case 'delayed':
                                    $statusClass = 'bg-danger';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'bg-secondary';
                                    break;
                                case 'picked_up':
                                    $statusClass = 'bg-info';
                                    break;
                                case 'on_hold':
                                    $statusClass = 'bg-warning text-dark';
                                    break;
                                case 'returned':
                                    $statusClass = 'bg-danger';
                                    break;
                            }
                            ?>
                            <span class="badge <?= $statusClass ?>">
                                <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $shipment['status']))) ?>
                            </span>
                            <?php if (!empty($shipment['current_location'])): ?>
                                <div class="mt-2 small text-muted">
                                    <strong>Current Location:</strong> <?= App\Core\View::e($shipment['current_location']) ?>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Origin:</th>
                        <td><?= App\Core\View::e($shipment['origin']) ?></td>
                    </tr>
                    <tr>
                        <th>Destination:</th>
                        <td><?= App\Core\View::e($shipment['destination']) ?></td>
                    </tr>
                    <tr>
                        <th>Weight:</th>
                        <td><?= isset($shipment['total_weight']) && $shipment['total_weight'] ? App\Core\View::e($shipment['total_weight']) . ' kg' : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Estimated Delivery:</th>
                        <td><?= isset($shipment['expected_delivery_date']) && $shipment['expected_delivery_date'] ? App\Core\View::e(date('Y-m-d', strtotime($shipment['expected_delivery_date']))) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Created:</th>
                        <td><?= App\Core\View::e(date('Y-m-d H:i', strtotime($shipment['created_at']))) ?></td>
                    </tr>
                    <tr>
                        <th>Last Updated:</th>
                        <td><?= App\Core\View::e(date('Y-m-d H:i', strtotime($shipment['updated_at']))) ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">Notes</div>
            <div class="card-body">
                <?php if (empty($shipment['comments'])): ?>
                    <p class="text-muted">No notes available.</p>
                <?php else: ?>
                    <p><?= nl2br(App\Core\View::e($shipment['comments'])) ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">Sender Information</div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>Name:</th>
                        <td><?= App\Core\View::e($shipment['shipper_name'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td><?= App\Core\View::e($shipment['shipper_phone'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Address:</th>
                        <td><?= nl2br(App\Core\View::e($shipment['shipper_address'] ?? 'N/A')) ?></td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td><?= App\Core\View::e($shipment['shipper_email'] ?? 'N/A') ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">Recipient Information</div>
            <div class="card-body">
                <table class="table-container">
                    <tr>
                        <th>Name:</th>
                        <td><?= App\Core\View::e($shipment['receiver_name'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td><?= App\Core\View::e($shipment['receiver_phone'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Address:</th>
                        <td><?= nl2br(App\Core\View::e($shipment['receiver_address'] ?? 'N/A')) ?></td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td><?= App\Core\View::e($shipment['receiver_email'] ?? 'N/A') ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span>Shipment History</span>
        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#addHistoryModal">
            <i class="fas fa-plus"></i> Add History Entry
        </button>
    </div>
    <div class="card-body">
        <?php if (empty($history)): ?>
            <p class="text-muted">No history entries found.</p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Status</th>
                            <th>Location</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($history as $entry): ?>
                            <tr>
                                <td><?= App\Core\View::e(date('Y-m-d H:i', strtotime($entry['date_time'] ?? $entry['timestamp']))) ?></td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-primary';
                                    switch ($entry['status']) {
                                        case 'pending':
                                            $statusClass = 'bg-warning text-dark';
                                            break;
                                        case 'in_transit':
                                            $statusClass = 'bg-info';
                                            break;
                                        case 'out_for_delivery':
                                            $statusClass = 'bg-primary';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'bg-success';
                                            break;
                                        case 'delayed':
                                            $statusClass = 'bg-danger';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-secondary';
                                            break;
                                        case 'picked_up':
                                            $statusClass = 'bg-info';
                                            break;
                                        case 'on_hold':
                                            $statusClass = 'bg-warning text-dark';
                                            break;
                                        case 'returned':
                                            $statusClass = 'bg-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>">
                                        <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $entry['status']))) ?>
                                    </span>
                                </td>
                                <td><?= App\Core\View::e($entry['location']) ?></td>
                                <td><?= App\Core\View::e($entry['message'] ?? $entry['description']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Document Management Section -->
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span>Document Management</span>
        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#requestDocumentModal">
            <i class="fas fa-file-plus"></i> Request Document
        </button>
    </div>
    <div class="card-body">
        <div id="document-requests-list">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading document requests...</span>
            </div>
        </div>
    </div>
</div>

<!-- Add History Modal -->
<div class="modal" id="addHistoryModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Shipment Status</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?= App\Core\View::url('/admin/shipments/add-history/' . $shipment['id']) ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status_date">Date</label>
                                <input type="date" id="status_date" name="status_date" class="form-control"
                                    value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status_time">Time</label>
                                <input type="time" id="status_time" name="status_time" class="form-control"
                                    value="<?= date('H:i') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" class="form-control"
                            value="<?= App\Core\View::e($shipment['destination']) ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" class="form-control" required>
                            <option value="">-- Select Status --</option>
                            <?php
                            // Use status options from database if available
                            if (isset($statusOptions) && !empty($statusOptions)) {
                                foreach ($statusOptions as $option):
                                    if (!$option['is_active']) continue;
                                    $value = $option['name'];
                                    $label = ucfirst(str_replace('_', ' ', $option['name']));
                                    $selected = $shipment['status'] === $value ? 'selected' : '';
                            ?>
                                <option value="<?= $value ?>" <?= $selected ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php
                                endforeach;
                            } else {
                                // Fallback to hardcoded statuses
                                $statuses = ['pending' => 'Pending', 'in_transit' => 'In Transit', 'out_for_delivery' => 'Out for Delivery', 'delivered' => 'Delivered', 'delayed' => 'Delayed', 'cancelled' => 'Cancelled'];
                                foreach ($statuses as $value => $label):
                            ?>
                                <option value="<?= $value ?>" <?= $shipment['status'] === $value ? 'selected' : '' ?>>
                                    <?= App\Core\View::e($label) ?>
                                </option>
                            <?php
                                endforeach;
                            }
                            ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Remarks</label>
                        <textarea id="description" name="description" class="form-control" rows="3"
                            placeholder="Enter any additional information about this status update"></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="notify_receiver" name="notify_receiver" value="1" checked>
                        <label class="form-check-label" for="notify_receiver">Notify receiver about this update</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Request Document Modal -->
<div class="modal" id="requestDocumentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Request Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="requestDocumentForm" action="<?= App\Core\View::url('/admin/shipments/request-document/' . $shipment['id']) ?>" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="document_type_id">Document Type</label>
                                <select id="document_type_id" name="document_type_id" class="form-control" required>
                                    <option value="">-- Select Document Type --</option>
                                    <!-- Options will be loaded via JavaScript from API -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="priority">Priority</label>
                                <select id="priority" name="priority" class="form-control" required>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="due_date">Due Date (Optional)</label>
                        <input type="datetime-local" id="due_date" name="due_date" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="request_message">Request Message</label>
                        <textarea id="request_message" name="request_message" class="form-control" rows="4"
                                  placeholder="Explain why this document is needed and any specific requirements..." required></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="send_notification" name="send_notification" value="1" checked>
                        <label class="form-check-label" for="send_notification">Send email notification to recipient</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Request Document</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Document Review Modal -->
<div class="modal" id="reviewDocumentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="reviewDocumentForm" method="POST">
                <?= App\Core\View::csrfField() ?>
                <div class="modal-body">
                    <div id="document-preview">
                        <!-- Document preview will be loaded here -->
                    </div>

                    <div class="form-group mt-3">
                        <label for="review_notes">Review Notes</label>
                        <textarea id="review_notes" name="review_notes" class="form-control" rows="3"
                                  placeholder="Add any notes about your decision..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="reviewDocument('rejected')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                    <button type="button" class="btn btn-success" onclick="reviewDocument('approved')">
                        <i class="fas fa-check"></i> Approve
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Simple modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Show modal
        const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const modal = document.querySelector(targetId);
                if (modal) {
                    modal.style.display = 'block';
                }
            });
        });

        // Close modal
        const closeBtns = document.querySelectorAll('[data-dismiss="modal"]');
        closeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    // Remove any backdrop
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                event.target.classList.remove('show');
                // Remove any backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }
        });
    });
</script>

<?php include __DIR__ . '/partials/_edit_modal.php'; ?>

<script>
    function openEditModal(shipmentId) {
        // Open the modal
        const modal = new bootstrap.Modal(document.getElementById('editShipmentModal'));
        modal.show();

        // Load the shipment data
        window.loadShipmentForEdit(shipmentId);
    }

    // Document Management Functions
    let currentDocumentRequestId = null;
    let currentDocumentUploadId = null;

    document.addEventListener('DOMContentLoaded', function() {
        loadDocumentTypes();
        loadDocumentRequests();

        // Handle document request form submission
        document.getElementById('requestDocumentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitDocumentRequest();
        });
    });

    async function loadDocumentTypes() {
        try {
            const response = await fetch('<?= App\Core\View::url('/api/document-types.php') ?>');
            const result = await response.json();

            const select = document.getElementById('document_type_id');
            select.innerHTML = '<option value="">-- Select Document Type --</option>';

            if (result.success && result.types) {
                result.types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    if (type.description) {
                        option.title = type.description;
                    }
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading document types:', error);
        }
    }

    async function loadDocumentRequests() {
        try {
            const shipmentId = <?= $shipment['id'] ?>;
            const response = await fetch(`/courier/public/api/admin-document-requests.php?shipment_id=${shipmentId}`);
            const result = await response.json();

            const container = document.getElementById('document-requests-list');

            if (result.success && result.requests.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
                html += '<th>Document Type</th><th>Priority</th><th>Status</th><th>Due Date</th><th>Requested</th><th>Actions</th>';
                html += '</tr></thead><tbody>';

                result.requests.forEach(request => {
                    const priorityClass = getPriorityClass(request.priority);
                    const statusClass = getStatusClass(request.status);
                    const isOverdue = request.due_date && new Date(request.due_date) < new Date();

                    html += `<tr ${isOverdue ? 'class="table-warning"' : ''}>`;
                    html += `<td>${request.document_type_name}</td>`;
                    html += `<td><span class="badge ${priorityClass}">${request.priority.toUpperCase()}</span></td>`;
                    html += `<td><span class="badge ${statusClass}">${formatStatus(request.status)}</span></td>`;
                    html += `<td>${request.due_date ? formatDate(request.due_date) : 'No deadline'}</td>`;
                    html += `<td>${formatDate(request.created_at)}</td>`;
                    html += `<td>${generateActionButtons(request)}</td>`;
                    html += '</tr>';
                });

                html += '</tbody></table></div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<p class="text-muted">No document requests found.</p>';
            }
        } catch (error) {
            console.error('Error loading document requests:', error);
            document.getElementById('document-requests-list').innerHTML =
                '<p class="text-danger">Error loading document requests.</p>';
        }
    }

    function generateActionButtons(request) {
        let buttons = '';

        switch (request.status) {
            case 'pending':
                buttons += `<button class="btn btn-sm btn-warning" onclick="cancelDocumentRequest(${request.id})">
                    <i class="fas fa-times"></i> Cancel
                </button>`;
                break;

            case 'uploaded':
                buttons += `<button class="btn btn-sm btn-primary" onclick="reviewDocument(${request.id}, ${request.uploaded_document?.id})">
                    <i class="fas fa-eye"></i> Review
                </button>`;
                break;

            case 'approved':
            case 'rejected':
                buttons += `<button class="btn btn-sm btn-light" onclick="viewDocumentHistory(${request.id})">
                    <i class="fas fa-history"></i> History
                </button>`;
                break;
        }

        return buttons;
    }

    function getPriorityClass(priority) {
        const classes = {
            'low': 'badge-secondary',
            'medium': 'badge-warning',
            'high': 'badge-danger',
            'urgent': 'badge-dark'
        };
        return classes[priority] || 'badge-secondary';
    }

    function getStatusClass(status) {
        const classes = {
            'pending': 'badge-warning',
            'uploaded': 'badge-info',
            'approved': 'badge-success',
            'rejected': 'badge-danger',
            'cancelled': 'badge-secondary'
        };
        return classes[status] || 'badge-secondary';
    }

    function formatStatus(status) {
        const labels = {
            'pending': 'Pending Upload',
            'uploaded': 'Under Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'cancelled': 'Cancelled'
        };
        return labels[status] || status;
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    let isSubmittingDocumentRequest = false;

    async function submitDocumentRequest() {
        // Prevent multiple submissions
        if (isSubmittingDocumentRequest) {
            return;
        }

        try {
            isSubmittingDocumentRequest = true;
            const form = document.getElementById('requestDocumentForm');
            const submitButton = form.querySelector('button[type="submit"]');

            // Disable submit button
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            }

            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Close modal properly
                const modal = document.getElementById('requestDocumentModal');
                if (window.jQuery && window.jQuery.fn.modal) {
                    $('#requestDocumentModal').modal('hide');
                } else {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    // Remove backdrop if it exists
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                    // Remove modal-open class from body
                    document.body.classList.remove('modal-open');
                }

                // Show success message first
                showAlert('Document has been requested successfully', 'success');

                // Reload document requests
                loadDocumentRequests();

                // Reset form
                form.reset();
            } else {
                showAlert(result.message || 'Error sending document request', 'danger');
            }
        } catch (error) {
            console.error('Error submitting document request:', error);
            showAlert('Error sending document request', 'danger');
        } finally {
            isSubmittingDocumentRequest = false;
            const form = document.getElementById('requestDocumentForm');
            const submitButton = form.querySelector('button[type="submit"]');

            // Re-enable submit button
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-paper-plane"></i> Send Request';
            }
        }
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;

        // Insert at the top of the page
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    // Function to cancel a document request
    async function cancelDocumentRequest(requestId) {
        if (!requestId) return;
        if (!confirm('Are you sure you want to cancel this document request?')) return;

        try {
            const formData = new FormData();
            formData.append('request_id', requestId);
            const response = await fetch('/courier/public/api/cancel-document-request.php', {
                method: 'POST',
                body: formData
            });
            const result = await response.json();
            if (result.success) {
                showAlert('Document request cancelled successfully!', 'success');
                loadDocumentRequests();
            } else {
                showAlert(result.message || 'Failed to cancel document request', 'danger');
            }
        } catch (error) {
            console.error('Error cancelling document request:', error);
            showAlert('Error cancelling document request', 'danger');
        }
    }

    // Function to open document review modal
    function reviewDocument(requestId, uploadId) {
        // If requestId is null/undefined, try to get it from the upload
        if (!requestId && uploadId) {
            // Fetch the document info to get the request ID
            fetch(`/courier/public/api/document-preview.php?upload_id=${uploadId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.upload) {
                        currentDocumentRequestId = data.upload.document_request_id;
                        currentDocumentUploadId = uploadId;

                        // Load document preview
                        loadDocumentPreview(uploadId);

                        // Show modal
                        const modal = document.getElementById('reviewDocumentModal');
                        if (modal) {
                            modal.style.display = 'block';
                            modal.classList.add('show');
                        }
                    } else {
                        alert('Error: Could not load document information');
                    }
                })
                .catch(error => {
                    console.error('Error fetching document info:', error);
                    alert('Error: Could not load document information');
                });
        } else {
            currentDocumentRequestId = requestId;
            currentDocumentUploadId = uploadId;

            // Load document preview
            loadDocumentPreview(uploadId);

            // Show modal
            const modal = document.getElementById('reviewDocumentModal');
            if (modal) {
                modal.style.display = 'block';
                modal.classList.add('show');
            }
        }
    }

    // Function to load document preview
    async function loadDocumentPreview(uploadId) {
        try {
            const response = await fetch(`/courier/public/api/document-preview.php?upload_id=${uploadId}`);
            const result = await response.json();

            const previewDiv = document.getElementById('document-preview');
            if (result.success) {
                const upload = result.upload;
                previewDiv.innerHTML = `
                    <div class="document-info">
                        <h6>Document Information</h6>
                        <p><strong>Filename:</strong> ${upload.original_filename}</p>
                        <p><strong>Size:</strong> ${formatFileSize(upload.file_size)}</p>
                        <p><strong>Uploaded:</strong> ${upload.uploaded_at_formatted}</p>
                        ${upload.upload_notes ? `<p><strong>Notes:</strong> ${upload.upload_notes}</p>` : ''}
                    </div>
                    <div class="document-preview-area">
                        ${generateDocumentPreview(upload)}
                    </div>
                `;
            } else {
                previewDiv.innerHTML = `<div class="alert alert-danger">Error loading document: ${result.message}</div>`;
            }
        } catch (error) {
            console.error('Error loading document preview:', error);
            document.getElementById('document-preview').innerHTML =
                `<div class="alert alert-danger">Error loading document preview</div>`;
        }
    }

    // Function to generate document preview based on file type
    function generateDocumentPreview(upload) {
        const fileExtension = upload.original_filename.split('.').pop().toLowerCase();
        const filePath = upload.file_path.replace('../', '/courier/public/');

        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
            return `<img src="${filePath}" alt="Document Preview" style="max-width: 100%; height: auto;">`;
        } else if (fileExtension === 'pdf') {
            return `
                <div class="pdf-preview">
                    <iframe src="${filePath}" width="100%" height="400px" style="border: 1px solid #ddd;"></iframe>
                    <p class="mt-2">
                        <a href="${filePath}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> Open in new tab
                        </a>
                    </p>
                </div>
            `;
        } else {
            return `
                <div class="file-download">
                    <i class="fas fa-file fa-3x text-muted"></i>
                    <p>Preview not available for this file type.</p>
                    <a href="${filePath}" download class="btn btn-primary">
                        <i class="fas fa-download"></i> Download File
                    </a>
                </div>
            `;
        }
    }

    // Function to submit document review (approve/reject)
    async function submitDocumentReview(status) {
        if (!currentDocumentRequestId || !currentDocumentUploadId) {
            alert('Error: Missing document information');
            return;
        }

        const reviewNotes = document.getElementById('review_notes').value;

        try {
            const formData = new FormData();
            formData.append('request_id', currentDocumentRequestId);
            formData.append('upload_id', currentDocumentUploadId);
            formData.append('status', status);
            formData.append('review_notes', reviewNotes);

            const response = await fetch('/courier/public/api/document-review.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Close modal
                const modal = document.getElementById('reviewDocumentModal');
                modal.style.display = 'none';
                modal.classList.remove('show');

                // Reload document requests
                loadDocumentRequests();

                // Show success message
                showAlert(`Document ${status} successfully!`, 'success');

                // Reset form
                document.getElementById('review_notes').value = '';
            } else {
                showAlert(result.message || `Error ${status} document`, 'danger');
            }
        } catch (error) {
            console.error('Error submitting review:', error);
            showAlert(`Error ${status} document`, 'danger');
        }
    }

    // Update the modal button onclick handlers to use the correct function
    document.addEventListener('DOMContentLoaded', function() {
        // Fix the modal buttons to call the correct function
        const rejectBtn = document.querySelector('#reviewDocumentModal .btn-danger');
        const approveBtn = document.querySelector('#reviewDocumentModal .btn-success');

        if (rejectBtn) {
            rejectBtn.onclick = () => submitDocumentReview('rejected');
        }
        if (approveBtn) {
            approveBtn.onclick = () => submitDocumentReview('approved');
        }
    });

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Function to view document history
    async function viewDocumentHistory(requestId) {
        try {
            const response = await fetch(`/courier/public/api/document-history.php?request_id=${requestId}`);
            const result = await response.json();

            if (result.success) {
                // Create a modal to show document history
                const historyModal = document.createElement('div');
                historyModal.className = 'modal fade';
                historyModal.id = 'documentHistoryModal';
                historyModal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content" style="border: 1px solid #dee2e6; border-radius: 8px;">
                            <div class="modal-header" style="border-bottom: 1px solid #dee2e6;">
                                <h5 class="modal-title">
                                    <i class="fas fa-history"></i> Document History
                                </h5>
                                <button type="button" class="close" onclick="closeDocumentHistoryModal()" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="timeline">
                                    ${generateHistoryTimeline(result.history)}
                                </div>
                            </div>
                            <div class="modal-footer" style="border-top: 1px solid #dee2e6;">
                                <button type="button" class="btn btn-light" onclick="closeDocumentHistoryModal()">Close</button>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if present
                const existingModal = document.getElementById('documentHistoryModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Add modal to page
                document.body.appendChild(historyModal);

                // Show modal
                const modalElement = document.getElementById('documentHistoryModal');
                modalElement.style.display = 'block';
                modalElement.classList.add('show');

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'documentHistoryBackdrop';
                document.body.appendChild(backdrop);

                // Close modal when clicking backdrop
                backdrop.addEventListener('click', closeDocumentHistoryModal);
            } else {
                showAlert(result.message || 'Error loading document history', 'danger');
            }
        } catch (error) {
            console.error('Error loading document history:', error);
            showAlert('Error loading document history', 'danger');
        }
    }

    // Function to close document history modal
    function closeDocumentHistoryModal() {
        const modal = document.getElementById('documentHistoryModal');
        const backdrop = document.getElementById('documentHistoryBackdrop');

        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.remove();
        }

        if (backdrop) {
            backdrop.remove();
        }
    }

    // Function to preview document from history
    function previewHistoryDocument(uploadId) {
        // Close the history modal first
        closeDocumentHistoryModal();

        // Open the document review modal with the upload
        reviewDocument(null, uploadId);
    }

    // Function to generate history timeline HTML
    function generateHistoryTimeline(history) {
        if (!history || history.length === 0) {
            return '<p class="text-muted">No history available for this document.</p>';
        }

        let html = '<div class="timeline-container">';

        history.forEach((entry, index) => {
            const isLast = index === history.length - 1;
            const statusClass = getStatusClass(entry.status);

            html += `
                <div class="timeline-item">
                    <div class="timeline-marker ${statusClass}">
                        <i class="fas ${getStatusIcon(entry.status)}"></i>
                    </div>
                    ${!isLast ? '<div class="timeline-line"></div>' : ''}
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h6 class="mb-1">${entry.status_display || entry.status}</h6>
                            <small class="text-muted">${entry.created_at_formatted || entry.created_at}</small>
                        </div>
                        ${entry.notes ? `<p class="mb-1">${entry.notes}</p>` : ''}
                        ${entry.user_name ? `<small class="text-muted">by ${entry.user_name}</small>` : ''}
                        ${entry.upload_id ? `
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="previewHistoryDocument(${entry.upload_id})">
                                    <i class="fas fa-eye"></i> Preview Document
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        html += '</div>';

        // Add timeline CSS
        html += `
            <style>
                .timeline-container {
                    position: relative;
                    padding-left: 30px;
                }
                .timeline-item {
                    position: relative;
                    margin-bottom: 20px;
                }
                .timeline-marker {
                    position: absolute;
                    left: -30px;
                    top: 0;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                }
                .timeline-line {
                    position: absolute;
                    left: -18px;
                    top: 24px;
                    width: 2px;
                    height: calc(100% + 20px);
                    background-color: #dee2e6;
                }
                .timeline-content {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    border-left: 3px solid #dee2e6;
                }
                .timeline-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;
                }
                .bg-pending { background-color: #ffc107; }
                .bg-uploaded { background-color: #17a2b8; }
                .bg-approved { background-color: #28a745; }
                .bg-rejected { background-color: #dc3545; }
                .bg-cancelled { background-color: #6c757d; }
            </style>
        `;

        return html;
    }

    // Helper function to get status CSS class
    function getStatusClass(status) {
        const statusClasses = {
            'pending': 'bg-pending',
            'uploaded': 'bg-uploaded',
            'approved': 'bg-approved',
            'rejected': 'bg-rejected',
            'cancelled': 'bg-cancelled'
        };
        return statusClasses[status] || 'bg-secondary';
    }

    // Helper function to get status icon
    function getStatusIcon(status) {
        const statusIcons = {
            'pending': 'fa-clock',
            'uploaded': 'fa-upload',
            'approved': 'fa-check',
            'rejected': 'fa-times',
            'cancelled': 'fa-ban'
        };
        return statusIcons[status] || 'fa-circle';
    }
</script>
