<?php
// Load appearance settings for theming
$appearanceSettings = [];
try {
    App\Core\Database::prepare("SELECT setting_key, setting_value FROM settings WHERE category = 'appearance'");
    App\Core\Database::execute();
    $results = App\Core\Database::fetchAll();

    foreach ($results as $setting) {
        $appearanceSettings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    // Use defaults if settings not found
}

// Load general settings for app name and logo
$generalSettings = [];
try {
    App\Core\Database::prepare("SELECT setting_key, setting_value FROM settings WHERE category = 'general'");
    App\Core\Database::execute();
    $results = App\Core\Database::fetchAll();

    foreach ($results as $setting) {
        $generalSettings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    // Use defaults if settings not found
}

$appName = $generalSettings['app_name'] ?? 'ELTA COURIER';
$companyLogo = $appearanceSettings['company_logo'] ?? '';
$primaryColor = $appearanceSettings['primary_color'] ?? '#2c3e50';
$secondaryColor = $appearanceSettings['secondary_color'] ?? '#e74c3c';
$sidebarBgColor = $appearanceSettings['sidebar_bg_color'] ?? '#ffffff';
$sidebarTextColor = $appearanceSettings['sidebar_text_color'] ?? '#666666';
$sidebarHoverColor = $appearanceSettings['sidebar_hover_color'] ?? '#f0f0f0';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Your Shipment - <?= App\Core\Config::get('app.name', 'Courier Service') ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/public-style.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/track-result.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/tracking-details.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/custom-theme.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<style>
/* Icon-Only Sidebar Styles */
.track-sidebar {
    width: 70px;
    background: <?= $sidebarBgColor ?>;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    height: 100vh;
    z-index: 1000;
    left: 0;
    top: 0;
}

.sidebar-brand {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: <?= $primaryColor ?>;
    color: white;
    font-size: 20px;
    font-weight: bold;
    text-decoration: none;
}

.sidebar-brand img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.nav-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: <?= $sidebarTextColor ?>;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
}

.nav-icon:hover {
    background: <?= $sidebarHoverColor ?>;
    color: <?= $primaryColor ?>;
    transform: translateY(-2px);
}

.nav-icon.active {
    background: <?= $primaryColor ?>;
    color: white;
}

.nav-icon i {
    font-size: 18px;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: <?= $secondaryColor ?>;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.nav-icon::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 60px;
    top: 50%;
    transform: translateY(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1001;
}

.nav-icon:hover::after {
    opacity: 1;
    visibility: visible;
    left: 65px;
}

/* Adjust main content for new sidebar */
.tracking-content {
    margin-left: 70px !important;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: <?= $primaryColor ?>;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: <?= $primaryColor ?>;
    border-bottom-color: <?= $primaryColor ?>;
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.mark-all-read-btn {
    margin-bottom: 15px;
    padding: 8px 16px;
    background: <?= $primaryColor ?>;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.mark-all-read-btn:hover {
    background: <?= $secondaryColor ?>;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Document Modal Styles */
.document-tab-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.document-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.document-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.document-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.document-info i {
    color: <?= $primaryColor ?>;
}

.document-name {
    font-weight: 600;
    color: #333;
}

.priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.priority-high {
    background: #fee;
    color: #c53030;
}

.priority-medium {
    background: #fef5e7;
    color: #d69e2e;
}

.priority-low {
    background: #f0fff4;
    color: #38a169;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background: #fef5e7;
    color: #d69e2e;
}

.status-approved {
    background: #f0fff4;
    color: #38a169;
}

.status-cancelled {
    background: #fee;
    color: #c53030;
}

.document-message {
    background: #f7fafc;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
    font-size: 14px;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.document-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #718096;
    margin: 10px 0;
}

.due-date.overdue {
    color: #c53030;
    font-weight: 600;
}

.document-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.btn-upload, .btn-download {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.btn-upload {
    background: <?= $primaryColor ?>;
    color: white;
}

.btn-upload:hover {
    background: <?= $secondaryColor ?>;
}

.btn-download {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-download:hover {
    background: #cbd5e0;
}

/* Enhanced Notifications Modal Styles */
.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.notification-item {
    display: flex;
    gap: 12px;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.notification-item.unread {
    background: #f0fff4;
    border: 1px solid #c6f6d5;
}

.notification-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: <?= $primaryColor ?>;
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.notification-time {
    font-size: 12px;
    color: #718096;
    white-space: nowrap;
}

.unread-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: <?= $secondaryColor ?>;
    margin-left: 8px;
}

.notification-message {
    margin: 0;
    font-size: 14px;
    color: #4a5568;
    line-height: 1.5;
}

.notification-actions {
    margin-top: 10px;
}

.notification-action-btn {
    padding: 6px 12px;
    background: <?= $primaryColor ?>;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    transition: background 0.3s ease;
}

.notification-action-btn:hover {
    background: <?= $secondaryColor ?>;
    color: white;
}

/* Notification type specific styles */
.notification-status_update .notification-icon {
    background: #3182ce;
}

.notification-document_request .notification-icon {
    background: #d69e2e;
}

.notification-delivery .notification-icon {
    background: #38a169;
}

.notification-delay .notification-icon {
    background: #e53e3e;
}

.notification-general .notification-icon {
    background: #718096;
}

.no-content, .error-content {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
}

.no-content i, .error-content i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.error-content {
    color: #e53e3e;
}

/* Document Management Tab System */
.document-management-section {
    margin-bottom: 30px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.section-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-icon {
    color: <?= $primaryColor ?>;
    font-size: 18px;
}

.section-title {
    margin: 0;
    color: <?= $primaryColor ?>;
    font-size: 18px;
}

.tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    background: #e9ecef;
    color: <?= $primaryColor ?>;
}

.tab-btn.active {
    color: <?= $primaryColor ?>;
    border-bottom-color: <?= $primaryColor ?>;
    background: white;
}

.tab-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: <?= $secondaryColor ?>;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.tab-content {
    display: none;
    padding: 20px;
    min-height: 200px;
}

.tab-content.active {
    display: block;
}

.loading-documents {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
}

.loading-documents i {
    font-size: 24px;
    margin-bottom: 10px;
    color: <?= $primaryColor ?>;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
    color: <?= $primaryColor ?>;
}

/* Upload Modal Styles */
.upload-modal .modal-content {
    max-width: 600px;
}

.upload-section {
    margin-bottom: 20px;
}

.file-upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    background: #f7fafc;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.file-upload-area:hover {
    border-color: <?= $primaryColor ?>;
    background: #edf2f7;
}

.upload-icon {
    font-size: 48px;
    color: <?= $primaryColor ?>;
    margin-bottom: 16px;
}

.upload-text {
    color: #4a5568;
    line-height: 1.5;
}

.upload-text strong {
    color: <?= $primaryColor ?>;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f0fff4;
    border: 1px solid #c6f6d5;
    border-radius: 6px;
}

.file-info i {
    color: #38a169;
    font-size: 18px;
}

.file-name {
    flex: 1;
    font-weight: 500;
    color: #2d3748;
}

.remove-file {
    background: #fed7d7;
    color: #c53030;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-notes {
    margin-bottom: 20px;
}

.upload-notes label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2d3748;
}

.upload-notes textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-family: inherit;
    resize: vertical;
}

.upload-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-cancel {
    padding: 10px 20px;
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.btn-cancel:hover {
    background: #cbd5e0;
}

.btn-upload-submit {
    padding: 10px 20px;
    background: <?= $primaryColor ?>;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-upload-submit:hover:not(:disabled) {
    background: <?= $secondaryColor ?>;
}

.btn-upload-submit:disabled {
    background: #a0aec0;
    cursor: not-allowed;
}

/* Legacy notification modal styles for compatibility */
.notification-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification-modal-content {
  background: #fff;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 24px rgba(0,0,0,0.18);
  padding: 24px 20px 16px 20px;
  position: relative;
}
.notification-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.notification-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}
.notification-modal-footer {
  text-align: right;
  margin-top: 16px;
}
.notification-modal-time {
  color: #888;
  font-size: 0.95em;
  margin-bottom: 8px;
}
</style>
</head>
<body class="tracking-page-body">
    <div class="tracking-container tracking-result-container">
        <div class="tracking-layout">
            <!-- Icon-Only Sidebar -->
            <div class="track-sidebar">
                <!-- Logo/Brand -->
                <div class="sidebar-brand">
                    <?php if (!empty($companyLogo)): ?>
                        <img src="<?= App\Core\View::asset($companyLogo) ?>" alt="<?= htmlspecialchars($appName) ?>">
                    <?php else: ?>
                        <?= strtoupper(substr($appName, 0, 1)) ?>
                    <?php endif; ?>
                </div>

                <!-- Navigation Icons -->
                <div class="sidebar-nav">
                    <button class="nav-icon" data-tooltip="Documents" onclick="openDocumentsModal()">
                        <i class="fas fa-file-alt"></i>
                        <span class="notification-badge" id="document-count-badge" style="display: none;">0</span>
                    </button>

                    <button class="nav-icon" data-tooltip="Notifications" onclick="openNotificationsModal()">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count-badge" style="display: none;">0</span>
                    </button>

                    <button class="nav-icon active" data-tooltip="Shipment Details" onclick="scrollToSection('shipment-details')">
                        <i class="fas fa-box"></i>
                    </button>

                    <button class="nav-icon" data-tooltip="Tracking History" onclick="scrollToSection('tracking-history')">
                        <i class="fas fa-route"></i>
                    </button>
                </div>
            </div>

            <!-- Main Content -->
            <div class="tracking-content">
                <div class="tracking-header">
                    <h1 class="tracking-title">Tracking List</h1>
                    <p class="tracking-subtitle">Track your shipment</p>
                </div>

                <div class="tracking-main">
                    <div class="tracking-search-container">
                        <form id="tracking-form" class="tracking-form" action="<?= App\Core\View::url('/api/track') ?>" method="POST">
                            <?= App\Core\View::csrfField() ?>
                            <div class="search-input-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="tracking_number" name="tracking_number" class="search-input" placeholder="Enter tracking number..." required>
                                <button type="submit" class="search-button">
                                    <i class="fas fa-search"></i> Track
                                </button>
                            </div>
                            <p class="tracking-note">
                                <i class="fas fa-info-circle note-icon"></i>
                                <span>Please Note: Shipment information will appear approximately 8 hours after being handed in at a <?= App\Models\Setting::get('site_name', 'Courier Service') ?> location</span>
                            </p>
                        </form>
                    </div>

                    <div id="tracking-results" class="tracking-results-area">
                        <!-- Results will be loaded here via JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Documents Modal -->
    <div class="modal" id="documentsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Document Requests</h3>
                <button class="close-btn" onclick="closeModal('documentsModal')">&times;</button>
            </div>
            <div class="modal-tabs">
                <button class="tab-btn active" onclick="switchModalTab('pending')">Pending Upload</button>
                <button class="tab-btn" onclick="switchModalTab('cancelled')">Cancelled</button>
                <button class="tab-btn" onclick="switchModalTab('approved')">Approved</button>
            </div>
            <div class="modal-body">
                <div class="tab-content active" id="modal-pending-tab">
                    <div id="pending-documents-content">
                        <p>Loading pending document requests...</p>
                    </div>
                </div>
                <div class="tab-content" id="modal-cancelled-tab">
                    <div id="cancelled-documents-content">
                        <p>Loading cancelled document requests...</p>
                    </div>
                </div>
                <div class="tab-content" id="modal-approved-tab">
                    <div id="approved-documents-content">
                        <p>Loading approved documents...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Modal -->
    <div class="modal" id="notificationsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Notifications</h3>
                <button class="close-btn" onclick="closeModal('notificationsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <button class="mark-all-read-btn" onclick="markAllNotificationsAsRead()">Mark All as Read</button>
                <div id="notifications-content">
                    <p>Loading notifications...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="<?= App\Core\View::asset('js/public-script.js') ?>"></script>
    <script src="<?= App\Core\View::asset('js/tracking-details.js') ?>"></script>

    <script>
        // Icon-Only Sidebar Functions
        function openDocumentsModal() {
            document.getElementById('documentsModal').classList.add('show');
            loadDocumentRequestsForModal();
        }

        function openNotificationsModal() {
            document.getElementById('notificationsModal').classList.add('show');
            loadNotificationsForModal();
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // Tab switching for documents modal
        function switchModalTab(tabName) {
            // Remove active from all modal tabs and content
            document.querySelectorAll('#documentsModal .tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('#documentsModal .tab-content').forEach(content => content.classList.remove('active'));

            // Add active to clicked tab and corresponding content
            event.target.classList.add('active');
            document.getElementById(`modal-${tabName}-tab`).classList.add('active');
        }

        // Smooth scrolling to sections
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Mark all notifications as read
        function markAllNotificationsAsRead() {
            fetch('/courier/public/api/mark-all-notifications-read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide all notification badges
                    document.querySelectorAll('.notification-badge').forEach(badge => {
                        badge.style.display = 'none';
                    });
                    // Reload notifications
                    loadNotificationsForModal();
                }
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
        }

        // Load document requests for modal
        function loadDocumentRequestsForModal() {
            // Get the current tracking number from the page
            const trackingNumber = getCurrentTrackingNumber();
            if (trackingNumber && typeof window.originalLoadDocumentRequests === 'function') {
                window.originalLoadDocumentRequests(trackingNumber);
            } else {
                // Fallback - show placeholder content
                document.getElementById('pending-documents-content').innerHTML = '<p>No tracking number available</p>';
                document.getElementById('cancelled-documents-content').innerHTML = '<p>No tracking number available</p>';
                document.getElementById('approved-documents-content').innerHTML = '<p>No tracking number available</p>';
            }
        }

        // Load notifications for modal
        function loadNotificationsForModal() {
            // Get the current tracking number from the page
            const trackingNumber = getCurrentTrackingNumber();
            if (trackingNumber && typeof window.originalLoadNotifications === 'function') {
                window.originalLoadNotifications(trackingNumber);
            } else {
                // Fallback - show placeholder content
                document.getElementById('notifications-content').innerHTML = '<p>No tracking number available</p>';
            }
        }

        // Helper function to get current tracking number
        function getCurrentTrackingNumber() {
            // Try to get tracking number from URL or page data
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('tracking_number') || window.currentTrackingNumber || null;
        }

        // Document Management Tab Switching (for main section)
        function switchDocumentTab(tabName) {
            // Remove active class from all tabs and content in main section
            document.querySelectorAll('.document-management-section .tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.document-management-section .tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            const clickedBtn = document.querySelector(`.document-management-section [data-tab="${tabName}-tab"]`);
            const targetContent = document.getElementById(`${tabName}-tab`);

            if (clickedBtn && targetContent) {
                clickedBtn.classList.add('active');
                targetContent.classList.add('active');
            }
        }

        // Make tab switching functions globally available
        window.switchDocumentTab = switchDocumentTab;
        window.switchModalTab = switchModalTab;

        // Document upload function
        function openDocumentUpload(requestId) {
            // Create upload modal
            const uploadModal = document.createElement('div');
            uploadModal.className = 'modal upload-modal';
            uploadModal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Upload Document</h3>
                        <button class="close-btn" onclick="closeUploadModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="upload-form-${requestId}" enctype="multipart/form-data">
                            <div class="upload-section">
                                <label for="file-input-${requestId}" class="file-upload-area">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div class="upload-text">
                                        <strong>Click to upload</strong> or drag and drop
                                        <br>
                                        <small>PDF, DOC, DOCX, JPG, PNG (Max 10MB)</small>
                                    </div>
                                    <input type="file" id="file-input-${requestId}" name="document" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                                </label>
                                <div id="file-info-${requestId}" class="file-info" style="display: none;">
                                    <i class="fas fa-file"></i>
                                    <span class="file-name"></span>
                                    <button type="button" class="remove-file" onclick="removeSelectedFile(${requestId})">&times;</button>
                                </div>
                            </div>
                            <div class="upload-notes">
                                <label for="notes-${requestId}">Additional Notes (Optional):</label>
                                <textarea id="notes-${requestId}" name="notes" rows="3" placeholder="Add any additional information about this document..."></textarea>
                            </div>
                            <div class="upload-actions">
                                <button type="button" class="btn-cancel" onclick="closeUploadModal()">Cancel</button>
                                <button type="submit" class="btn-upload-submit" disabled>Upload Document</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(uploadModal);
            uploadModal.classList.add('show');

            // Add file input handler
            const fileInput = document.getElementById(`file-input-${requestId}`);
            const fileInfo = document.getElementById(`file-info-${requestId}`);
            const uploadBtn = uploadModal.querySelector('.btn-upload-submit');
            const uploadArea = uploadModal.querySelector('.file-upload-area');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    fileInfo.querySelector('.file-name').textContent = file.name;
                    fileInfo.style.display = 'flex';
                    uploadArea.style.display = 'none';
                    uploadBtn.disabled = false;
                }
            });

            // Add form submit handler
            document.getElementById(`upload-form-${requestId}`).addEventListener('submit', function(e) {
                e.preventDefault();
                uploadDocument(requestId);
            });
        }

        function closeUploadModal() {
            const modal = document.querySelector('.upload-modal');
            if (modal) {
                modal.remove();
            }
        }

        function removeSelectedFile(requestId) {
            const fileInput = document.getElementById(`file-input-${requestId}`);
            const fileInfo = document.getElementById(`file-info-${requestId}`);
            const uploadBtn = document.querySelector('.upload-modal .btn-upload-submit');
            const uploadArea = document.querySelector('.upload-modal .file-upload-area');

            fileInput.value = '';
            fileInfo.style.display = 'none';
            uploadArea.style.display = 'flex';
            uploadBtn.disabled = true;
        }

        function uploadDocument(requestId) {
            const form = document.getElementById(`upload-form-${requestId}`);
            const formData = new FormData(form);
            formData.append('request_id', requestId);

            const uploadBtn = form.querySelector('.btn-upload-submit');
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

            // Simulate upload (replace with actual API call)
            setTimeout(() => {
                alert('Document uploaded successfully!');
                closeUploadModal();
                // Reload document requests to show updated status
                if (typeof window.originalLoadDocumentRequests === 'function') {
                    const trackingNumber = getCurrentTrackingNumber();
                    if (trackingNumber) {
                        window.originalLoadDocumentRequests(trackingNumber);
                    }
                }
            }, 2000);
        }

        // Make upload functions globally available
        window.openDocumentUpload = openDocumentUpload;
        window.closeUploadModal = closeUploadModal;
        window.removeSelectedFile = removeSelectedFile;
        window.uploadDocument = uploadDocument;

        // Close modals when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.classList.remove('show');
            }
        }

        // Update notification badges when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // This would be called by existing tracking functionality
            // to update badge counts
        });
    </script>
</body>
</html>
