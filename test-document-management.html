<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Document Management Tabs</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
        }

        /* Document Management Section */
        .document-management-section {
            margin-bottom: 30px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            color: #2c3e50;
            font-size: 18px;
        }

        .section-title {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        /* Tab System */
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn:hover {
            background: #e9ecef;
            color: #2c3e50;
        }

        .tab-btn.active {
            color: #2c3e50;
            border-bottom-color: #2c3e50;
            background: white;
        }

        .tab-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* Tab Content */
        .tab-content {
            display: none;
            padding: 20px;
            min-height: 200px;
        }

        .tab-content.active {
            display: block;
        }

        /* Document Items */
        .document-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .document-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .document-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .document-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .document-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .document-info i {
            color: #2c3e50;
            font-size: 16px;
        }

        .document-name {
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fef5e7;
            color: #d69e2e;
        }

        .status-approved {
            background: #f0fff4;
            color: #38a169;
        }

        .status-cancelled {
            background: #fee;
            color: #c53030;
        }

        .document-message {
            background: #f7fafc;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
            color: #4a5568;
            border-left: 3px solid #2c3e50;
        }

        .document-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #718096;
            margin: 10px 0;
        }

        .document-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .btn-upload {
            background: #2c3e50;
            color: white;
        }

        .btn-upload:hover {
            background: #34495e;
        }

        .btn-download {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-download:hover {
            background: #cbd5e0;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* Test Controls */
        .test-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .test-controls h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .test-controls button {
            margin-right: 10px;
            margin-bottom: 10px;
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .test-controls button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Document Management Tab System Test</h1>
            <p>Testing the tab system for pending, cancelled, and approved documents</p>
        </div>

        <div class="content">
            <!-- Test Controls -->
            <div class="test-controls">
                <h3>Test Controls</h3>
                <button onclick="loadSampleData()">Load Sample Data</button>
                <button onclick="clearAllData()">Clear All Data</button>
                <button onclick="addPendingDocument()">Add Pending Document</button>
                <button onclick="addApprovedDocument()">Add Approved Document</button>
                <button onclick="addCancelledDocument()">Add Cancelled Document</button>
            </div>

            <!-- Document Management Section -->
            <div class="document-management-section" id="document-management-section">
                <div class="section-header">
                    <i class="fas fa-file-alt section-icon"></i>
                    <h3 class="section-title">Document Management</h3>
                </div>

                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="pending-tab" onclick="switchDocumentTab('pending')">
                        Pending Upload
                        <span class="tab-badge" id="pending-badge" style="display: none;">0</span>
                    </button>
                    <button class="tab-btn" data-tab="cancelled-tab" onclick="switchDocumentTab('cancelled')">
                        Cancelled
                        <span class="tab-badge" id="cancelled-badge" style="display: none;">0</span>
                    </button>
                    <button class="tab-btn" data-tab="approved-tab" onclick="switchDocumentTab('approved')">
                        Approved
                        <span class="tab-badge" id="approved-badge" style="display: none;">0</span>
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content active" id="pending-tab">
                    <div id="pending-documents-content">
                        <div class="empty-state">
                            <i class="fas fa-file-alt"></i>
                            <p>No pending document requests</p>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="cancelled-tab">
                    <div id="cancelled-documents-content">
                        <div class="empty-state">
                            <i class="fas fa-times-circle"></i>
                            <p>No cancelled document requests</p>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="approved-tab">
                    <div id="approved-documents-content">
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p>No approved documents</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data for testing
        const sampleDocuments = {
            pending: [
                {
                    id: 1,
                    document_type_name: 'Commercial Invoice',
                    status: 'pending',
                    priority: 'high',
                    request_message: 'Please upload the commercial invoice for customs clearance.',
                    created_at: '2024-01-15 10:30:00',
                    due_date: '2024-01-20 23:59:59'
                },
                {
                    id: 2,
                    document_type_name: 'Packing List',
                    status: 'pending',
                    priority: 'medium',
                    request_message: 'Detailed packing list required for inspection.',
                    created_at: '2024-01-16 14:20:00',
                    due_date: '2024-01-22 23:59:59'
                }
            ],
            cancelled: [
                {
                    id: 3,
                    document_type_name: 'Certificate of Origin',
                    status: 'cancelled',
                    priority: 'low',
                    request_message: 'Certificate of origin was requested but later cancelled.',
                    created_at: '2024-01-10 09:15:00',
                    cancelled_at: '2024-01-12 16:30:00'
                }
            ],
            approved: [
                {
                    id: 4,
                    document_type_name: 'Bill of Lading',
                    status: 'approved',
                    priority: 'high',
                    request_message: 'Bill of lading has been reviewed and approved.',
                    created_at: '2024-01-08 11:45:00',
                    approved_at: '2024-01-14 13:20:00',
                    file_path: '/documents/bill-of-lading-123.pdf'
                },
                {
                    id: 5,
                    document_type_name: 'Insurance Certificate',
                    status: 'approved',
                    priority: 'medium',
                    request_message: 'Insurance certificate approved for shipment.',
                    created_at: '2024-01-09 15:30:00',
                    approved_at: '2024-01-15 10:15:00',
                    file_path: '/documents/insurance-cert-456.pdf'
                }
            ]
        };

        // Tab switching function
        function switchDocumentTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            const clickedBtn = document.querySelector(`[data-tab="${tabName}-tab"]`);
            const targetContent = document.getElementById(`${tabName}-tab`);
            
            if (clickedBtn && targetContent) {
                clickedBtn.classList.add('active');
                targetContent.classList.add('active');
            }
        }

        // Generate document HTML
        function generateDocumentHTML(documents, type) {
            if (!documents || documents.length === 0) {
                const icons = {
                    pending: 'fa-file-alt',
                    cancelled: 'fa-times-circle',
                    approved: 'fa-check-circle'
                };
                const messages = {
                    pending: 'No pending document requests',
                    cancelled: 'No cancelled document requests',
                    approved: 'No approved documents'
                };
                
                return `
                    <div class="empty-state">
                        <i class="fas ${icons[type]}"></i>
                        <p>${messages[type]}</p>
                    </div>
                `;
            }

            let html = '<div class="document-list">';
            
            documents.forEach(doc => {
                const isOverdue = doc.due_date && new Date(doc.due_date) < new Date();
                
                html += `
                    <div class="document-item">
                        <div class="document-header">
                            <div class="document-info">
                                <i class="fas fa-file-alt"></i>
                                <span class="document-name">${doc.document_type_name}</span>
                                ${doc.priority ? `<span class="priority-badge priority-${doc.priority}">${doc.priority.toUpperCase()}</span>` : ''}
                            </div>
                            <span class="status-badge status-${doc.status}">${formatStatus(doc.status)}</span>
                        </div>
                        
                        ${doc.request_message ? `<div class="document-message">${doc.request_message}</div>` : ''}
                        
                        <div class="document-meta">
                            <span>Requested: ${formatDate(doc.created_at)}</span>
                            ${doc.due_date ? `<span class="${isOverdue ? 'overdue' : ''}">Due: ${formatDate(doc.due_date)}</span>` : ''}
                            ${doc.approved_at ? `<span>Approved: ${formatDate(doc.approved_at)}</span>` : ''}
                            ${doc.cancelled_at ? `<span>Cancelled: ${formatDate(doc.cancelled_at)}</span>` : ''}
                        </div>
                        
                        ${type === 'pending' ? `
                            <div class="document-actions">
                                <button class="btn btn-upload" onclick="uploadDocument(${doc.id})">
                                    <i class="fas fa-upload"></i> Upload Document
                                </button>
                            </div>
                        ` : ''}
                        
                        ${type === 'approved' && doc.file_path ? `
                            <div class="document-actions">
                                <a href="${doc.file_path}" class="btn btn-download" target="_blank">
                                    <i class="fas fa-download"></i> Download
                                </a>
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // Update document tabs
        function updateDocumentTabs(documents) {
            // Update content
            document.getElementById('pending-documents-content').innerHTML = 
                generateDocumentHTML(documents.pending, 'pending');
            document.getElementById('cancelled-documents-content').innerHTML = 
                generateDocumentHTML(documents.cancelled, 'cancelled');
            document.getElementById('approved-documents-content').innerHTML = 
                generateDocumentHTML(documents.approved, 'approved');

            // Update badges
            updateBadge('pending-badge', documents.pending?.length || 0);
            updateBadge('cancelled-badge', documents.cancelled?.length || 0);
            updateBadge('approved-badge', documents.approved?.length || 0);
        }

        // Update badge
        function updateBadge(badgeId, count) {
            const badge = document.getElementById(badgeId);
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        // Utility functions
        function formatStatus(status) {
            const statusLabels = {
                'pending': 'Pending Upload',
                'cancelled': 'Cancelled',
                'approved': 'Approved'
            };
            return statusLabels[status] || status;
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Test functions
        function loadSampleData() {
            updateDocumentTabs(sampleDocuments);
        }

        function clearAllData() {
            updateDocumentTabs({ pending: [], cancelled: [], approved: [] });
        }

        function addPendingDocument() {
            const newDoc = {
                id: Date.now(),
                document_type_name: 'Test Document',
                status: 'pending',
                priority: 'medium',
                request_message: 'This is a test document request.',
                created_at: new Date().toISOString(),
                due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
            };
            sampleDocuments.pending.push(newDoc);
            updateDocumentTabs(sampleDocuments);
        }

        function addApprovedDocument() {
            const newDoc = {
                id: Date.now(),
                document_type_name: 'Test Approved Document',
                status: 'approved',
                priority: 'low',
                request_message: 'This document has been approved.',
                created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                approved_at: new Date().toISOString(),
                file_path: '/test/document.pdf'
            };
            sampleDocuments.approved.push(newDoc);
            updateDocumentTabs(sampleDocuments);
        }

        function addCancelledDocument() {
            const newDoc = {
                id: Date.now(),
                document_type_name: 'Test Cancelled Document',
                status: 'cancelled',
                priority: 'high',
                request_message: 'This document request was cancelled.',
                created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                cancelled_at: new Date().toISOString()
            };
            sampleDocuments.cancelled.push(newDoc);
            updateDocumentTabs(sampleDocuments);
        }

        function uploadDocument(docId) {
            alert(`Upload document functionality for document ID: ${docId}`);
        }

        // Initialize with sample data
        document.addEventListener('DOMContentLoaded', function() {
            loadSampleData();
        });
    </script>
</body>
</html>